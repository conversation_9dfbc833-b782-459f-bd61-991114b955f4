global:
  scrape_interval: 5s
  external_labels:
    cluster: 'nova-proxy'
    replica: 'vm-1'

scrape_configs:
  # VictoriaMetrics self-monitoring
  - job_name: 'victoriametrics'
    static_configs:
      - targets: ['localhost:8428']

  # Nova Proxy Application Metrics (new port 10001)
  - job_name: 'nova-proxy'
    static_configs:
      - targets: ['nova-proxy:10001']
    metrics_path: '/metrics'
    scrape_interval: 5s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']
