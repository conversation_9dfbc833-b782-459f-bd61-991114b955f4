package main

import (
	"log"
	"nova-proxy/internal/controllers"
)

func main() {
	// Create and initialize application controller
	app := controllers.NewApplicationController()

	if err := app.Initialize(); err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	// Start all services
	if err := app.Start(); err != nil {
		log.Fatalf("Failed to start application: %v", err)
	}

	// Wait for shutdown
	app.Wait()
}
