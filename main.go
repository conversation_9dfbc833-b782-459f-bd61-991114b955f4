package main

import (
	"encoding/json"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

type RequestConfig struct {
	Path    string            `json:"path"`
	Format  string            `json:"format"`
	Headers map[string]string `json:"headers"`
	Data    map[string]string `json:"data"`
}

type APIConfig struct {
	Request   RequestConfig            `json:"request"`
	Responses []map[string]interface{} `json:"responses"`
}

func main() {
	r := gin.Default()

	// Load all API configs from api/ directory
	configs := loadAPIConfigs("api/")

	// Setup routes dynamically
	for endpoint, config := range configs {
		setupRoute(r, endpoint, config)
	}

	r.Run(":8000")
}

func loadAPIConfigs(dir string) map[string]*APIConfig {
	configs := make(map[string]*APIConfig)

	files, _ := filepath.Glob(filepath.Join(dir, "*.json"))
	for _, file := range files {
		data, _ := os.ReadFile(file)

		var config APIConfig
		json.Unmarshal(data, &config)

		endpoint := strings.TrimSuffix(filepath.Base(file), ".json")
		configs[endpoint] = &config
	}

	return configs
}

func setupRoute(r *gin.Engine, endpoint string, config *APIConfig) {
	// Use the path from config, fallback to endpoint if not specified
	path := config.Request.Path
	if path == "" {
		path = "/" + endpoint
	}

	// Setup route handler
	r.POST(path, func(c *gin.Context) {
		// Validate required headers
		if !validateHeaders(c, config.Request.Headers) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Missing or invalid required headers",
			})
			return
		}

		// Validate request format and data
		if !validateRequestData(c, config.Request) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid request format or missing required data",
			})
			return
		}

		// Select a random response from the responses array
		if len(config.Responses) == 0 {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "No responses configured",
			})
			return
		}

		// Seed random number generator
		selectedResponse := config.Responses[rand.Intn(len(config.Responses))]

		c.JSON(http.StatusOK, selectedResponse)
	})
}

func validateHeaders(c *gin.Context, requiredHeaders map[string]string) bool {
	for headerName, expectedValue := range requiredHeaders {
		actualValue := c.GetHeader(headerName)

		// Exact match required
		if actualValue != expectedValue {
			return false
		}
	}
	return true
}

func validateRequestData(c *gin.Context, reqConfig RequestConfig) bool {
	// Check content type matches expected format
	contentType := c.GetHeader("Content-Type")

	switch reqConfig.Format {
	case "multipart/form-data":
		if !strings.Contains(contentType, "multipart/form-data") {
			return false
		}
		// Validate required form fields
		for fieldName, fieldType := range reqConfig.Data {
			if fieldType == "file" {
				// Check if file field exists
				_, _, err := c.Request.FormFile(fieldName)
				if err != nil {
					return false
				}
			} else {
				// Check if form value exists
				if c.PostForm(fieldName) == "" {
					return false
				}
			}
		}
	case "application/json", "json":
		if !strings.Contains(contentType, "application/json") {
			return false
		}
		// For JSON, we could add more specific validation here
		var jsonData map[string]interface{}
		if err := c.ShouldBindJSON(&jsonData); err != nil {
			return false
		}
		// Validate required JSON fields
		for fieldName := range reqConfig.Data {
			if _, exists := jsonData[fieldName]; !exists {
				return false
			}
		}
	default:
		// If no specific format is required, allow any content type
		return true
	}

	return true
}
