{"functions": [{"name": "ocr", "service": "ocr", "description": "Optical Character Recognition service for document processing", "requires_auth": true, "rate_limit": {"requests_per_second": 50, "requests_per_minute": 2000}}, {"name": "liveness", "service": "liveness", "description": "Liveness detection service for biometric verification", "requires_auth": true, "rate_limit": {"requests_per_second": 30, "requests_per_minute": 1200}}, {"name": "facematch", "service": "facematch", "description": "Face matching service for identity verification", "requires_auth": true, "rate_limit": {"requests_per_second": 25, "requests_per_minute": 1000}}]}