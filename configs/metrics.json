{"enabled": true, "endpoint": "/metrics", "namespace": "nova_proxy", "subsystem": "", "collection": {"request_metrics": true, "auth_metrics": true, "rate_limit_metrics": true, "upstream_metrics": true, "service_metrics": true, "business_metrics": true}, "histogram_buckets": {"request_duration": [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10], "request_size": [1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072, 262144, 524288, 1048576], "response_size": [1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072, 262144, 524288, 1048576]}, "labels": {"include_client_labels": true, "include_partner_labels": true, "include_service_labels": true, "include_business_code_labels": true, "max_label_values": 1000}, "sampling": {"enabled": false, "rate": 1.0, "high_cardinality_threshold": 10000}}