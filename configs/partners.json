{"services": {"ocr": {"partners": [{"name": "GTEL", "business_codes": ["banking", "finance"], "global_rate_limit": {"requests_per_second": 20, "requests_per_minute": 1000}, "function_rate_limits": [{"partner_name": "GTEL", "function_name": "ocr", "rate_limit": {"requests_per_second": 15, "requests_per_minute": 600}}], "response_mapping": {"error_code_field": "error.code", "message_field": "error.message", "mappers": {"gender": {"nam": "MALE", "nữ": "FEMALE"}, "frontType": {"chip_id_card_front": "front", "id_card_front": "front"}, "backType": {"id_card_back": "back"}, "code": {"ERROR_00": "BIO-00000", "ERROR_01": "BIO-00001"}}, "field_mappings": {"code": "error.code|map:code", "data.id": "data.person_number", "data.idConf": "data.person_number_confidence", "data.name": "data.full_name", "data.nameConf": "data.full_name_confidence", "data.birthday": "data.date_of_birth|date:dd/MM/yyyy->yyyy-MM-dd", "data.birthdayConf": "data.date_of_birth_confidence", "data.expiry": "data.date_of_expiry|date:dd/MM/yyyy->yyyy-MM-dd", "data.expiryConf": "data.date_of_expiry_confidence", "data.gender": "data.gender|map:gender", "data.genderConf": "data.gender_confidence", "data.hometown": "data.place_of_origin", "data.hometownConf": "data.place_of_origin_confidence", "data.address": "data.place_of_residence", "data.addressConf": "data.place_of_residence_confidence", "data.nationality": "data.nationality", "data.nationalityConf": "data.nationality_confidence", "data.issueBy": "data.issued_at", "data.issueConf": "data.issued_at_confidence", "data.frontType": "data.front_type|map:frontType", "data.backType": "data.back_type|map:backType", "data.front_invalid_code": "data.front_invalid_code"}}, "upstreams": [{"url": "http://localhost:8000/gtel/verify-ocrid", "format": "multipart/form-data", "weight": 10, "field_mapping": {"img1": "img1", "img2": "img2"}, "hardcoded_headers": {"x-api-key": "apikey", "code": "VPSUAT"}, "environment": "development", "debug": false}, {"url": "http://localhost:8000/gtel2/verify-ocrid", "format": "multipart/form-data", "weight": 10, "field_mapping": {"img1": "img1", "img2": "img2"}, "hardcoded_headers": {"x-api-key": "apikey", "code": "VPSUAT2"}, "environment": "production", "debug": false}]}, {"name": "VVN", "business_codes": ["insurance", "healthcare"], "response_mapping": {"success_field": "success", "success_values": [true], "data_field": "data", "error_field": "error_message", "confidence_field": "confidence_score", "field_mappings": {"doc_type": "document_type", "text_content": "raw_text", "extracted_data.full_name": "name", "extracted_data.identification": "id_number", "extracted_data.birth_date": "date_of_birth", "extracted_data.residence": "address"}}, "upstreams": [{"url": "http://partner-b.example.com/api/ocr", "format": "multipart/form-data", "weight": 50, "field_mapping": {"img1": "document_front", "img2": "document_back"}, "hardcoded_headers": {"X-Secret-Key": "partner-b-secret-123", "X-Partner-ID": "nova-proxy-client"}, "region": "eu-west-1", "environment": "production"}]}, {"name": "partner-c", "business_codes": ["retail", "ecommerce"], "response_mapping": {"success_field": "code", "success_values": [0, "0"], "data_field": "payload", "error_code_field": "code", "message_field": "message", "confidence_field": "payload.accuracy", "field_mappings": {"payload.type": "document_type", "payload.content": "raw_text", "payload.person.name": "name", "payload.person.id": "id_number", "payload.person.dob": "date_of_birth"}}, "upstreams": [{"url": "http://partner-c.example.com/ocr/process", "format": "json", "weight": 20, "region": "ap-southeast-1", "environment": "production"}]}]}, "liveness": {"partners": [{"name": "partner-a", "business_codes": ["banking", "finance"], "response_mapping": {"success_field": "status", "success_values": ["success"], "data_field": "result", "confidence_field": "result.confidence", "field_mappings": {"result.is_alive": "is_live", "result.liveness_score": "score"}}, "upstreams": [{"url": "http://partner-a.example.com/liveness", "format": "json", "weight": 40, "region": "us-east-1", "environment": "production"}]}, {"name": "partner-d", "business_codes": ["insurance", "kyc"], "response_mapping": {"success_field": "success", "success_values": [true], "confidence_field": "confidence", "field_mappings": {"live": "is_live", "score": "score"}}, "upstreams": [{"url": "http://partner-d.example.com/api/liveness-check", "format": "multipart/form-data", "weight": 60, "region": "eu-central-1", "environment": "production"}]}]}, "facematch": {"partners": [{"name": "partner-b", "business_codes": ["all"], "response_mapping": {"success_field": "status", "success_values": ["ok"], "data_field": "match_result", "confidence_field": "match_result.confidence", "field_mappings": {"match_result.is_match": "is_match", "match_result.similarity_score": "similarity", "match_result.match_score": "score"}}, "upstreams": [{"url": "http://partner-b.example.com/api/face-match", "format": "json", "weight": 100, "debug": true, "region": "us-east-1", "environment": "production"}]}]}}}