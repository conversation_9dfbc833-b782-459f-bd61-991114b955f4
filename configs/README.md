# Configuration Structure

This directory contains the new modular configuration system for nova-proxy. The configuration is split into separate files for easier maintenance and organization.

## Configuration Files

### `partners.json`
Contains all partner and service configurations including:
- Service definitions (ocr, liveness, facematch)
- Partner configurations with business codes
- Upstream endpoints with load balancing weights
- Response mapping configurations
- Rate limiting settings per partner
- Hardcoded headers for authentication

### `clients.json`
Contains client authentication and authorization settings:
- Client definitions with tokens
- Allowed functions per client
- Rate limiting configurations per client
- Function-specific rate limits

### `functions.json`
Contains function definitions and global settings:
- Function metadata (name, service, description)
- Authentication requirements
- Global rate limits per function

### `logging.json`
Contains logging configuration:
- Log levels and output settings
- File rotation and compression
- Request/response logging options
- Performance and debugging settings

## Configuration Requirements

The application now requires the new multi-file configuration structure:

- **Required**: `configs/` directory must exist
- **Required**: All configuration files must be present and valid
- **No fallback**: Legacy single-file configuration is no longer supported

If any required configuration files are missing, the application will fail to start with a clear error message indicating which configuration is missing.

## Benefits of New Structure

1. **Separation of Concerns**: Each configuration file has a specific purpose
2. **Easier Maintenance**: Changes to partners don't affect client configurations
3. **Better Organization**: Related configurations are grouped together
4. **Modular Updates**: Individual components can be updated independently
5. **Version Control Friendly**: Smaller files with focused changes

## Configuration Loading

The configuration is loaded by the `config.Manager` which:
1. Detects configuration style (new vs legacy)
2. Loads all configuration files
3. Initializes and configures all services
4. Provides a clean interface to main.go

This approach keeps all configuration complexity out of the main application logic.
