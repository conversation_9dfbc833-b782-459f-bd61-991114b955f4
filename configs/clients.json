{"enabled": true, "clients": [{"name": "banking-client", "token": "sk-banking-1234567890abcdef", "allowed_functions": ["ocr", "liveness"], "enabled": true, "description": "Banking application client with OCR and liveness access", "rate_limit": 100, "global_rate_limit": {"requests_per_second": 10, "requests_per_minute": 500}, "function_rate_limits": [{"client_name": "banking-client", "function_name": "ocr", "rate_limit": {"requests_per_second": 5, "requests_per_minute": 200}}, {"client_name": "banking-client", "function_name": "liveness", "rate_limit": {"requests_per_second": 3, "requests_per_minute": 100}}], "created_at": "2025-09-15T15:00:00Z"}, {"name": "insurance-client", "token": "sk-insurance-abcdef1234567890", "allowed_functions": ["ocr", "facematch"], "enabled": true, "description": "Insurance application client with OCR and face matching", "rate_limit": 50, "created_at": "2025-09-15T15:00:00Z"}, {"name": "kyc-client", "token": "sk-kyc-fedcba0987654321", "allowed_functions": ["*"], "enabled": true, "description": "KYC service with full access to all functions", "rate_limit": 200, "created_at": "2025-09-15T15:00:00Z"}, {"name": "mobile-app", "token": "sk-mobile-9876543210fedcba", "allowed_functions": ["liveness", "facematch"], "enabled": true, "description": "Mobile application for biometric verification", "rate_limit": 300, "created_at": "2025-09-15T15:00:00Z"}, {"name": "test-client", "token": "sk-test-****************", "allowed_functions": ["ocr"], "enabled": false, "description": "Test client for development (disabled)", "rate_limit": 10, "created_at": "2025-09-15T15:00:00Z"}]}