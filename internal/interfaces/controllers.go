package interfaces

import (
	"nova-proxy/internal/models"

	"github.com/gin-gonic/gin"
)

// ProxyRequest represents a proxy request
type ProxyRequest struct {
	Service   string
	Method    string
	Headers   map[string]string
	Body      []byte
	ClientIP  string
	UserAgent string
	AuthInfo  *models.AuthContext
}

// ProxyResponse represents a proxy response
type ProxyResponse struct {
	StatusCode int
	Headers    map[string]string
	Body       []byte
	Error      error
}

// ProxyController interface for proxy business logic
type ProxyController interface {
	HandleProxy(c *gin.Context)
}

// AdminController interface for admin business logic
type AdminController interface {
	UpdateConfig(config map[string]models.ServiceConfig) error
	GetConfig() (map[string]*models.ServiceConfig, error)
	GetServiceStatus() (map[string]interface{}, error)
	GetClients() ([]models.Client, error)
	GetClient(name string) (*models.Client, error)
}

// HealthController interface for health check logic
type HealthController interface {
	GetHealth() gin.H
	GetReadiness() (gin.H, int)
}

// ServerController interface for server management
type ServerController interface {
	Start() error
	Stop() error
	Wait()
}

// RouterController interface for router setup
type RouterController interface {
	SetupMainRouter() *gin.Engine
	SetupMetricsRouter() *gin.Engine
	SetupHealthRouter() *gin.Engine
}
