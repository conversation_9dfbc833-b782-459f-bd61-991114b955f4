package models

// MetricsConfig represents the metrics configuration
type MetricsConfig struct {
	Enabled    bool                    `json:"enabled"`
	Endpoint   string                  `json:"endpoint"`
	Namespace  string                  `json:"namespace"`
	Subsystem  string                  `json:"subsystem"`
	Collection MetricsCollectionConfig `json:"collection"`
	Buckets    MetricsBucketsConfig    `json:"histogram_buckets"`
	Labels     MetricsLabelsConfig     `json:"labels"`
	Sampling   MetricsSamplingConfig   `json:"sampling"`
}

// MetricsCollectionConfig controls which metrics to collect
type MetricsCollectionConfig struct {
	RequestMetrics   bool `json:"request_metrics"`
	AuthMetrics      bool `json:"auth_metrics"`
	RateLimitMetrics bool `json:"rate_limit_metrics"`
	UpstreamMetrics  bool `json:"upstream_metrics"`
	ServiceMetrics   bool `json:"service_metrics"`
	BusinessMetrics  bool `json:"business_metrics"`
}

// MetricsBucketsConfig defines histogram buckets for different metrics
type MetricsBucketsConfig struct {
	RequestDuration []float64 `json:"request_duration"`
	RequestSize     []float64 `json:"request_size"`
	ResponseSize    []float64 `json:"response_size"`
}

// MetricsLabelsConfig controls label inclusion and cardinality
type MetricsLabelsConfig struct {
	IncludeClientLabels      bool `json:"include_client_labels"`
	IncludePartnerLabels     bool `json:"include_partner_labels"`
	IncludeServiceLabels     bool `json:"include_service_labels"`
	IncludeBusinessCodeLabels bool `json:"include_business_code_labels"`
	MaxLabelValues           int  `json:"max_label_values"`
}

// MetricsSamplingConfig controls metrics sampling for high-cardinality scenarios
type MetricsSamplingConfig struct {
	Enabled                   bool    `json:"enabled"`
	Rate                      float64 `json:"rate"`
	HighCardinalityThreshold  int     `json:"high_cardinality_threshold"`
}

// DefaultMetricsConfig returns a default metrics configuration
func DefaultMetricsConfig() *MetricsConfig {
	return &MetricsConfig{
		Enabled:   true,
		Endpoint:  "/metrics",
		Namespace: "nova_proxy",
		Subsystem: "",
		Collection: MetricsCollectionConfig{
			RequestMetrics:   true,
			AuthMetrics:      true,
			RateLimitMetrics: true,
			UpstreamMetrics:  true,
			ServiceMetrics:   true,
			BusinessMetrics:  true,
		},
		Buckets: MetricsBucketsConfig{
			RequestDuration: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10},
			RequestSize:     []float64{1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072, 262144, 524288, 1048576},
			ResponseSize:    []float64{1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072, 262144, 524288, 1048576},
		},
		Labels: MetricsLabelsConfig{
			IncludeClientLabels:       true,
			IncludePartnerLabels:      true,
			IncludeServiceLabels:      true,
			IncludeBusinessCodeLabels: true,
			MaxLabelValues:            1000,
		},
		Sampling: MetricsSamplingConfig{
			Enabled:                  false,
			Rate:                     1.0,
			HighCardinalityThreshold: 10000,
		},
	}
}
