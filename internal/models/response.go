package models

import "time"

type StandardResponse struct {
	Timestamp time.Time              `json:"timestamp"`
	Duration  float64                `json:"duration_ms"`
	Engine    string                 `json:"engine"`
	RequestID string                 `json:"request_id"`
	Data      map[string]interface{} `json:"-,omitempty"`
}

type ErrorResponse struct {
	StandardResponse
	Code    string  `json:"code"`
	Message *string `json:"message"`
}

// ResponseMappingRule defines how to map partner responses to standard format
type ResponseMappingRule struct {
	ErrorCodeField string                       `json:"error_code_field,omitempty"`
	MessageField   string                       `json:"message_field,omitempty"`
	Mappers        map[string]map[string]string `json:"mappers"`
	FieldMappings  map[string]string            `json:"field_mappings"`
}
