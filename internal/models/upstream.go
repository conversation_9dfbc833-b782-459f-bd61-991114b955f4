package models

// PartnerFunctionRateLimit represents rate limits for a specific partner-function combination
type PartnerFunctionRateLimit struct {
	PartnerName  string          `json:"partner_name"`
	FunctionName string          `json:"function_name"`
	RateLimit    RateLimitConfig `json:"rate_limit"`
}

// Partner represents a partner configuration with multiple upstreams
type Partner struct {
	Name               string                     `json:"name"`
	BusinessCodes      []string                   `json:"business_codes,omitempty"`       // Business codes this partner handles
	ResponseMapping    *ResponseMappingRule       `json:"response_mapping,omitempty"`     // Response normalization config
	Upstreams          []Upstream                 `json:"upstreams"`                      // Multiple endpoints for this partner
	GlobalRateLimit    RateLimitConfig            `json:"global_rate_limit,omitempty"`    // Global rate limits for this partner
	FunctionRateLimits []PartnerFunctionRateLimit `json:"function_rate_limits,omitempty"` // Per-function rate limits
}

// Upstream represents a single upstream server endpoint
type Upstream struct {
	URL              string            `json:"url"`
	Format           string            `json:"format"`
	Weight           int               `json:"weight"`
	FieldMapping     map[string]string `json:"field_mapping,omitempty"`
	HardcodedFields  map[string]string `json:"hardcoded_fields,omitempty"`
	HardcodedHeaders map[string]string `json:"hardcoded_headers,omitempty"` // Headers to inject (API keys, auth tokens, etc.)
	Debug            bool              `json:"debug,omitempty"`
	Region           string            `json:"region,omitempty"`      // Optional region identifier
	Environment      string            `json:"environment,omitempty"` // Optional environment (prod, staging, etc.)
}

// ServiceConfig represents configuration for a service with multiple partners
type ServiceConfig struct {
	Partners []Partner `json:"partners"`
}
