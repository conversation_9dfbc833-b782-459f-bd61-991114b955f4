package models

import "time"

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	RequestsPerSecond int `json:"requests_per_second,omitempty"` // RPS limit (0 = unlimited)
	RequestsPerMinute int `json:"requests_per_minute,omitempty"` // RPM limit (0 = unlimited)
}

// ClientFunctionRateLimit represents rate limits for a specific client-function combination
type ClientFunctionRateLimit struct {
	ClientName   string          `json:"client_name"`
	FunctionName string          `json:"function_name"`
	RateLimit    RateLimitConfig `json:"rate_limit"`
}

// Client represents an authenticated client with permissions
type Client struct {
	Name               string                    `json:"name"`                           // Client identifier
	Token              string                    `json:"token"`                          // Authentication token
	AllowedFunctions   []string                  `json:"allowed_functions"`              // Functions this client can call
	Enabled            bool                      `json:"enabled"`                        // Whether client is active
	CreatedAt          time.Time                 `json:"created_at,omitempty"`           // When client was created
	LastUsed           time.Time                 `json:"last_used,omitempty"`            // Last authentication time
	Description        string                    `json:"description,omitempty"`          // Optional client description
	RateLimit          int                       `json:"rate_limit,omitempty"`           // Legacy: Requests per minute (0 = unlimited)
	GlobalRateLimit    RateLimitConfig           `json:"global_rate_limit,omitempty"`    // Global rate limits for this client
	FunctionRateLimits []ClientFunctionRateLimit `json:"function_rate_limits,omitempty"` // Per-function rate limits
}

// AuthConfig represents the authentication configuration
type AuthConfig struct {
	Enabled bool     `json:"enabled"` // Whether auth is enabled globally
	Clients []Client `json:"clients"` // List of authorized clients
}

// AuthContext holds authentication information for a request
type AuthContext struct {
	Client        *Client   `json:"client,omitempty"`
	Authenticated bool      `json:"authenticated"`
	AuthMethod    string    `json:"auth_method,omitempty"` // "header", "token", etc.
	RequestTime   time.Time `json:"request_time"`
}

// Function represents a callable service function
type Function struct {
	Name         string          `json:"name"`                  // Function identifier (e.g., "ocr", "liveness")
	Service      string          `json:"service"`               // Service this function belongs to
	Description  string          `json:"description,omitempty"` // Function description
	RequiresAuth bool            `json:"requires_auth"`         // Whether this function requires authentication
	RateLimit    RateLimitConfig `json:"rate_limit,omitempty"`  // Global rate limits for this function
}

// AuthError represents authentication-related errors
type AuthError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *AuthError) Error() string {
	return e.Message
}

// Common auth error codes
const (
	AuthErrorMissingHeaders = "MISSING_AUTH_HEADERS"
	AuthErrorInvalidClient  = "INVALID_CLIENT"
	AuthErrorInvalidToken   = "INVALID_TOKEN"
	AuthErrorAccessDenied   = "ACCESS_DENIED"
	AuthErrorRateLimit      = "RATE_LIMIT_EXCEEDED"
	AuthErrorDisabled       = "CLIENT_DISABLED"
)
