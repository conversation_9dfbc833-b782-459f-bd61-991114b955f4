package routers

import (
	"nova-proxy/internal/interfaces"
	"nova-proxy/internal/middleware"

	"github.com/gin-gonic/gin"
)

// Proxy<PERSON><PERSON><PERSON> handles HTTP routing for proxy requests
type ProxyRouter struct {
	proxyController interfaces.ProxyController
}

// NewProxyRouter creates a new proxy router
func NewProxyRouter(proxyController interfaces.ProxyController) *ProxyRouter {
	return &ProxyRouter{
		proxyController: proxyController,
	}
}

// SetupRoutes configures proxy routes with middleware
func (pr *ProxyRouter) SetupRoutes(
	r *gin.Engine,
	authMiddleware *middleware.AuthMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
	metricsMiddleware *middleware.MetricsMiddleware,
	hasMetrics bool,
) {
	// Service routes with authentication and rate limiting
	serviceGroup := r.Group("/")
	serviceGroup.Use(authMiddleware.Authenticate())
	serviceGroup.Use(authMiddleware.RequireService())
	serviceGroup.Use(rateLimitMiddleware.CheckRateLimits())
	serviceGroup.Use(authMiddleware.AddAuthInfo())
	serviceGroup.Use(rateLimitMiddleware.AddRateLimitHeaders())

	// Add latency headers middleware
	serviceGroup.Use(metricsMiddleware.AddLatencyHeaders())

	// Add metrics middleware for auth and rate limiting
	if hasMetrics {
		serviceGroup.Use(metricsMiddleware.CollectAuthMetrics())
		serviceGroup.Use(metricsMiddleware.CollectRateLimitMetrics())
	}

	// Proxy routes
	serviceGroup.POST("/:service", pr.proxyController.HandleProxy) // /ocr, /liveness, /facematch
}
