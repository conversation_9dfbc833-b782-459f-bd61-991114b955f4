package routers

import (
	"nova-proxy/internal/handlers"
	"nova-proxy/internal/middleware"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

// AdminRouter handles HTTP routing for admin requests
type AdminRouter struct {
	adminHandler        *handlers.AdminHandler
	rateLimitMiddleware *middleware.RateLimitMiddleware
	loggerService       *services.LoggerService
}

// NewAdminRouter creates a new admin router
func NewAdminRouter(
	adminHandler *handlers.AdminHandler,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
	loggerService *services.LoggerService,
) *AdminRouter {
	return &AdminRouter{
		adminHandler:        adminHandler,
		rateLimitMiddleware: rateLimitMiddleware,
		loggerService:       loggerService,
	}
}

// SetupRoutes configures admin routes
func (ar *AdminRouter) SetupRoutes(r *gin.Engine, hasLogging bool) {
	// Admin routes
	adminGroup := r.Group("/admin")
	{
		// Configuration management routes
		adminGroup.POST("/config", ar.adminHandler.UpdateConfig)
		adminGroup.GET("/config", ar.adminHandler.GetConfig)
		adminGroup.GET("/status", ar.adminHandler.GetServiceStatus)

		// Auth management routes
		adminGroup.GET("/clients", ar.adminHandler.GetClients)
		adminGroup.GET("/clients/:name", ar.adminHandler.GetClient)

		// Rate limiting management routes
		adminGroup.GET("/ratelimit/stats", ar.rateLimitMiddleware.GetRateLimitStats())
		adminGroup.POST("/ratelimit/reset", ar.rateLimitMiddleware.ResetRateLimit())

		// Add logging stats endpoint if logging is enabled
		if hasLogging && ar.loggerService != nil {
			adminGroup.GET("/logging/stats", func(c *gin.Context) {
				stats := ar.loggerService.GetStats()
				c.JSON(200, gin.H{"logging_stats": stats})
			})
		}
	}
}
