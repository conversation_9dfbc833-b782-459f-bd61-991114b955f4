package routers

import (
	"nova-proxy/internal/interfaces"

	"github.com/gin-gonic/gin"
)

// HealthRouter handles HTTP routing for health check requests
type HealthRouter struct {
	healthController interfaces.HealthController
}

// NewHealthRouter creates a new health router
func NewHealthRouter(healthController interfaces.HealthController) *HealthRouter {
	return &HealthRouter{
		healthController: healthController,
	}
}

// SetupRoutes configures health check routes
func (hr *HealthRouter) SetupRoutes(r *gin.Engine) {
	r.GET("/health", func(c *gin.Context) {
		response := hr.healthController.GetHealth()
		c.JSON(200, response)
	})

	r.GET("/readiness", func(c *gin.Context) {
		response, statusCode := hr.healthController.GetReadiness()
		c.JSON(statusCode, response)
	})
}
