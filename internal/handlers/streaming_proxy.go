package handlers

import (
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// StreamingProxyHandler handles HTTP proxy requests with optimized file streaming
type StreamingProxyHandler struct {
	balancer         *services.Balancer
	streamingService *services.StreamingProxyService
	normalizer       *services.ResponseNormalizer
	loggerService    *services.LoggerService
	httpClient       *http.Client
}

// NewStreamingProxyHandler creates a new streaming proxy handler
func NewStreamingProxyHandler(balancer *services.Balancer, loggerService *services.LoggerService) *StreamingProxyHandler {
	// Create optimized HTTP client with connection pooling
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second, // Connection timeout
			KeepAlive: 30 * time.Second, // Keep-alive timeout
		}).Dial<PERSON>ontext,
		MaxIdleConns:        100,              // Maximum idle connections
		MaxIdleConnsPerHost: 10,               // Maximum idle connections per host
		IdleConnTimeout:     90 * time.Second, // Idle connection timeout
		TLSHandshakeTimeout: 10 * time.Second, // TLS handshake timeout
		DisableCompression:  false,            // Enable compression
		// Optimize for streaming
		WriteBufferSize: 64 * 1024, // 64KB write buffer
		ReadBufferSize:  64 * 1024, // 64KB read buffer
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second, // Overall request timeout
	}

	return &StreamingProxyHandler{
		balancer:         balancer,
		streamingService: services.NewStreamingProxyService(httpClient),
		normalizer:       services.NewResponseNormalizer(),
		loggerService:    loggerService,
		httpClient:       httpClient,
	}
}

// HandleStreamingProxy handles incoming proxy requests with optimized streaming
func (h *StreamingProxyHandler) HandleStreamingProxy(c *gin.Context) {
	// Record start time for proxy processing
	proxyStartTime := time.Now()

	service := c.Param("service")
	startTime := time.Now()

	// Set handler type for metrics
	c.Set("handler_type", "streaming")

	// Extract routing headers
	partnerName := c.GetHeader("x-partner")
	businessCode := c.GetHeader("x-biz")

	// Record routing start time
	routingStartTime := time.Now()

	// Get upstream and partner configuration
	upstream, partner := h.balancer.GetUpstreamWithRateLimit(service, partnerName, businessCode)

	// Record routing latency
	routingLatency := time.Since(routingStartTime)
	c.Set("routing_latency", routingLatency)
	if upstream == nil || partner == nil {
		c.JSON(400, gin.H{"error": "No available upstream for service"})
		return
	}

	// Store partner info in context for rate limiting middleware
	c.Set("selected_partner_name", partner.Name)
	c.Set("selected_partner", partner)
	c.Set("upstream_url", upstream.URL)
	c.Set("partner_name", partner.Name)
	c.Set("debug_mode", false)

	// Check if this is a multipart request (required for file streaming)
	contentType := c.GetHeader("Content-Type")
	if !strings.Contains(contentType, "multipart/form-data") {
		c.JSON(400, gin.H{"error": "Only multipart/form-data requests are supported for streaming"})
		return
	}

	// Parse streaming request without buffering file content
	streamingReq, err := h.streamingService.ParseStreamingRequest(c, service)
	if err != nil {
		c.JSON(400, gin.H{"error": fmt.Sprintf("Failed to parse request: %v", err)})
		return
	}

	// Validate request
	if err := h.streamingService.ValidateStreamingRequest(streamingReq, service); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Handle debug mode (if enabled)
	if upstream.Debug {
		h.handleStreamingDebugMode(c, service, upstream, partner, streamingReq)
		// Record proxy latency for debug mode
		proxyLatency := time.Since(proxyStartTime)
		c.Set("proxy_latency", proxyLatency)
		return
	}

	// Handle normal streaming proxy mode
	h.handleStreamingProxyMode(c, service, upstream, partner, streamingReq, startTime)

	// Record proxy latency for normal mode
	proxyLatency := time.Since(proxyStartTime)
	c.Set("proxy_latency", proxyLatency)
}

// handleStreamingDebugMode handles debug mode for streaming requests
func (h *StreamingProxyHandler) handleStreamingDebugMode(
	c *gin.Context,
	service string,
	upstream *models.Upstream,
	partner *models.Partner,
	streamingReq *services.StreamingMultipartRequest,
) {
	// For debug mode, we'll provide metadata without processing files
	debugResponse := gin.H{
		"debug_mode": true,
		"service":    service,
		"partner":    partner.Name,
		"upstream":   upstream.URL,
		"files":      make(map[string]gin.H),
		"fields":     streamingReq.Fields,
		"timestamp":  time.Now().UTC().Format(time.RFC3339),
	}

	// Add file metadata without reading content
	for fieldName, fileHeader := range streamingReq.Files {
		debugResponse["files"].(map[string]gin.H)[fieldName] = gin.H{
			"filename": fileHeader.Filename,
			"size":     fileHeader.Size,
			"headers":  fileHeader.Header,
		}
	}

	c.JSON(200, debugResponse)
}

// handleStreamingProxyMode processes requests in streaming proxy mode
func (h *StreamingProxyHandler) handleStreamingProxyMode(
	c *gin.Context,
	service string,
	upstream *models.Upstream,
	partner *models.Partner,
	streamingReq *services.StreamingMultipartRequest,
	startTime time.Time,
) {
	// Log request size for monitoring
	requestSize := h.streamingService.GetRequestSizeEstimate(streamingReq)
	if h.loggerService != nil {
		h.loggerService.LogInfo("Streaming proxy request", logrus.Fields{
			"service":      service,
			"partner":      partner.Name,
			"request_size": requestSize,
			"file_count":   len(streamingReq.Files),
			"field_count":  len(streamingReq.Fields),
		})
	}

	// Record upstream request start time
	upstreamStartTime := time.Now()

	// Stream request to upstream
	resp, err := h.streamingService.StreamToUpstream(c, upstream, partner, streamingReq, upstream.FieldMapping)

	// Record upstream latency
	upstreamLatency := time.Since(upstreamStartTime)
	c.Set("upstream_latency", upstreamLatency)

	if err != nil {
		c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to proxy request: %v", err)})
		return
	}
	defer resp.Body.Close()

	// Generate request ID for tracking
	requestID := fmt.Sprintf("%s-%d", service, time.Now().UnixNano())

	// Handle response based on whether normalization is needed
	if partner.ResponseMapping != nil {
		// For normalization, we need to read the response body
		// This is unavoidable for response transformation
		h.handleNormalizedResponse(c, resp, upstream, partner, service, startTime, requestID)
	} else {
		// Stream response directly for maximum performance
		if err := h.streamingService.StreamResponse(c, resp); err != nil {
			// Log error but don't try to send JSON response as headers are already sent
			if h.loggerService != nil {
				h.loggerService.LogError("Error streaming response", err, logrus.Fields{
					"service":    service,
					"partner":    partner.Name,
					"request_id": requestID,
				})
			}
		}
	}

	// Log completion
	duration := time.Since(startTime)
	if h.loggerService != nil {
		h.loggerService.LogInfo("Streaming proxy completed", logrus.Fields{
			"service":      service,
			"partner":      partner.Name,
			"duration_ms":  duration.Milliseconds(),
			"request_size": requestSize,
			"request_id":   requestID,
		})
	}
}

// handleNormalizedResponse handles responses that require normalization
func (h *StreamingProxyHandler) handleNormalizedResponse(
	c *gin.Context,
	resp *http.Response,
	upstream *models.Upstream,
	partner *models.Partner,
	service string,
	startTime time.Time,
	requestID string,
) {
	// Read response body for normalization
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to read partner response"})
		return
	}

	// Normalize response
	normalizedResp, err := h.normalizer.NormalizeResponse(
		responseBody, upstream, partner, service, startTime, requestID)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to normalize response"})
		return
	}

	c.JSON(resp.StatusCode, normalizedResp)
}

// GetMetrics returns performance metrics for the streaming proxy
func (h *StreamingProxyHandler) GetMetrics() gin.H {
	return gin.H{
		"handler_type":     "streaming_proxy",
		"optimization":     "file_streaming",
		"buffer_pooling":   true,
		"direct_streaming": true,
	}
}
