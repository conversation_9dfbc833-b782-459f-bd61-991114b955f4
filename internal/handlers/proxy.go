package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"time"

	"nova-proxy/internal/middleware"
	"nova-proxy/internal/models"
	"nova-proxy/internal/services"
	"nova-proxy/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ProxyHandler handles HTTP proxy requests
type Proxy<PERSON>andler struct {
	balancer      *services.Balancer
	proxyService  *services.ProxyService
	debugService  *services.DebugService
	normalizer    *services.ResponseNormalizer
	httpClient    *http.Client
	loggerService *services.LoggerService
}

// NewProxyHandler creates a new proxy handler
func NewProxyHandler(balancer *services.Balancer, proxyService *services.ProxyService, debugService *services.DebugService, loggerService *services.LoggerService) *ProxyHandler {
	// Create optimized HTTP client with connection pooling
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second, // Connection timeout
			KeepAlive: 30 * time.Second, // Keep-alive timeout
		}).DialContext,
		MaxIdleConns:        100,              // Maximum idle connections
		MaxIdleConnsPerHost: 10,               // Maximum idle connections per host
		IdleConnTimeout:     90 * time.Second, // Idle connection timeout
		TLSHandshakeTimeout: 10 * time.Second, // TLS handshake timeout
		DisableCompression:  false,            // Enable compression
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second, // Overall request timeout
	}

	return &ProxyHandler{
		balancer:      balancer,
		proxyService:  proxyService,
		debugService:  debugService,
		normalizer:    services.NewResponseNormalizer(),
		httpClient:    httpClient,
		loggerService: loggerService,
	}
}

// HandleProxy handles incoming proxy requests
func (h *ProxyHandler) HandleProxy(c *gin.Context) {
	// Record start time for proxy processing
	proxyStartTime := time.Now()

	service := c.Param("service")

	// Set handler type for metrics
	c.Set("handler_type", "standard")

	// Extract routing headers
	partnerName := c.GetHeader("x-partner")
	businessCode := c.GetHeader("x-biz")

	// Record routing start time
	routingStartTime := time.Now()

	// Get upstream using intelligent routing with rate limiting
	upstream, partner := h.balancer.GetUpstreamWithRateLimit(service, partnerName, businessCode)

	// Record routing latency
	routingLatency := time.Since(routingStartTime)
	c.Set("routing_latency", routingLatency)

	if upstream == nil {
		if h.loggerService != nil {
			h.loggerService.LogWarn("No upstream found for request", logrus.Fields{
				"service":       service,
				"partner_name":  partnerName,
				"business_code": businessCode,
				"client_ip":     c.ClientIP(),
				"user_agent":    c.GetHeader("User-Agent"),
			})
		}
		c.JSON(404, gin.H{"error": "No upstream found or all partners are rate limited"})
		return
	}

	// Store partner info in context for rate limiting middleware
	c.Set("selected_partner_name", partner.Name)
	c.Set("selected_partner", partner)

	// Bind and validate request
	reqStruct := utils.GetRequestStruct(service)
	if reqStruct == nil {
		c.JSON(400, gin.H{"error": "Invalid service"})
		return
	}

	if err := c.ShouldBind(reqStruct); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Apply field mapping
	mappedData := utils.ApplyMappingToReq(reqStruct, upstream.FieldMapping)

	// Handle debug mode
	if upstream.Debug {
		h.handleDebugMode(c, service, reqStruct, upstream, partner, mappedData)
		// Record proxy latency for debug mode
		proxyLatency := time.Since(proxyStartTime)
		c.Set("proxy_latency", proxyLatency)
		return
	}

	// Handle normal proxy mode
	h.handleProxyMode(c, upstream, partner, mappedData)

	// Record proxy latency for normal mode
	proxyLatency := time.Since(proxyStartTime)
	c.Set("proxy_latency", proxyLatency)
}

// handleDebugMode processes requests in debug mode
func (h *ProxyHandler) handleDebugMode(c *gin.Context, service string, reqStruct interface{}, upstream *models.Upstream, partner *models.Partner, mappedData map[string]interface{}) {
	// Set context values for logging middleware
	c.Set("upstream_url", upstream.URL)
	c.Set("partner_name", partner.Name)
	c.Set("debug_mode", true)

	var body io.Reader
	var contentType string
	var err error

	// Build body for debug sample
	if upstream.Format == "json" {
		jsonData, err := h.debugService.BuildJSONFromReqDebug(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		jsonBytes, _ := json.Marshal(jsonData)
		body = bytes.NewReader(jsonBytes)
		contentType = "application/json"
	} else {
		body, contentType, err = h.proxyService.BuildMultipartFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
	}

	// Return debug response with routing info
	debugResp := h.debugService.BuildDebugResponse(c, service, reqStruct, upstream, mappedData, contentType, body)

	// Add routing information to debug response
	if upstreamInfo, exists := debugResp["selected_upstream"].(gin.H); exists {
		upstreamInfo["partner_name"] = partner.Name
		upstreamInfo["business_codes"] = partner.BusinessCodes
		upstreamInfo["upstream_url"] = upstream.URL
		upstreamInfo["upstream_region"] = upstream.Region
		upstreamInfo["upstream_environment"] = upstream.Environment
		upstreamInfo["hardcoded_headers"] = upstream.HardcodedHeaders

		// Add authentication info
		if authCtx, exists := middleware.GetAuthContext(c); exists {
			authInfo := gin.H{
				"authenticated": authCtx.Authenticated,
				"auth_method":   authCtx.AuthMethod,
			}
			if authCtx.Client != nil {
				authInfo["client_name"] = authCtx.Client.Name
				authInfo["allowed_functions"] = authCtx.Client.AllowedFunctions
			}
			upstreamInfo["authentication"] = authInfo
		}

		// Determine routing reason
		routingReason := "weighted round-robin"
		headerPartnerName := c.GetHeader("x-partner")
		headerBusinessCode := c.GetHeader("x-biz")

		if headerPartnerName != "" {
			routingReason = "x-partner header"
		} else if headerBusinessCode != "" {
			routingReason = "x-biz header"
		}
		upstreamInfo["routing_reason"] = routingReason
		upstreamInfo["routing_headers"] = gin.H{
			"x-partner": headerPartnerName,
			"x-biz":     headerBusinessCode,
		}
	}

	c.JSON(200, debugResp)
}

// handleProxyMode processes requests in normal proxy mode
func (h *ProxyHandler) handleProxyMode(c *gin.Context, upstream *models.Upstream, partner *models.Partner, mappedData map[string]interface{}) {
	service := c.Param("service")
	startTime := time.Now()

	// Set context values for logging middleware
	c.Set("upstream_url", upstream.URL)
	c.Set("partner_name", partner.Name)
	c.Set("debug_mode", false)

	// Prepare request body
	body, contentType, err := h.proxyService.PrepareRequestBody(upstream, mappedData)
	if err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Create and configure proxy request
	req, err := http.NewRequest(c.Request.Method, upstream.URL, body)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to create request"})
		return
	}
	req.Header.Set("Content-Type", contentType)

	// Copy headers from original request
	for key, values := range c.Request.Header {
		if key != "Content-Length" {
			for _, value := range values {
				req.Header.Add(key, value)
			}
		}
	}

	// Add hardcoded headers (API keys, auth tokens, etc.)
	for key, value := range upstream.HardcodedHeaders {
		req.Header.Set(key, value)
	}

	// Record upstream request start time
	upstreamStartTime := time.Now()

	// Execute request to partner using optimized client
	resp, err := h.httpClient.Do(req)

	// Record upstream latency
	upstreamLatency := time.Since(upstreamStartTime)
	c.Set("upstream_latency", upstreamLatency)

	if err != nil {
		if h.loggerService != nil {
			h.loggerService.LogError("Failed to contact partner", err, logrus.Fields{
				"service":      service,
				"partner_name": partner.Name,
				"upstream_url": upstream.URL,
				"client_ip":    c.ClientIP(),
				"duration_ms":  time.Since(startTime).Milliseconds(),
			})
		}
		c.JSON(500, gin.H{"error": "Failed to contact partner"})
		return
	}
	defer resp.Body.Close()

	// Generate request ID for tracking
	requestID := fmt.Sprintf("%s-%d", service, time.Now().UnixNano())

	// Handle response based on whether normalization is needed
	if partner.ResponseMapping != nil {
		// For normalization, we need to read the response body
		responseBody, err := io.ReadAll(resp.Body)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to read partner response"})
			return
		}

		normalizedResp, err := h.normalizer.NormalizeResponse(
			responseBody, upstream, partner, service, startTime, requestID)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to normalize response"})
			return
		}
		c.JSON(resp.StatusCode, normalizedResp)
		return
	}

	// Stream raw response directly without reading into memory
	h.streamResponse(c, resp)

	// Log successful request completion
	if h.loggerService != nil {
		h.loggerService.LogInfo("Request completed successfully", logrus.Fields{
			"service":      service,
			"partner_name": partner.Name,
			"upstream_url": upstream.URL,
			"status_code":  resp.StatusCode,
			"duration_ms":  time.Since(startTime).Milliseconds(),
			"client_ip":    c.ClientIP(),
			"request_id":   requestID,
		})
	}
}

// streamResponse streams the response directly from upstream to client
func (h *ProxyHandler) streamResponse(c *gin.Context, resp *http.Response) {
	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			c.Header(key, value)
		}
	}

	// Set status code
	c.Status(resp.StatusCode)

	// Stream response body directly
	_, err := io.Copy(c.Writer, resp.Body)
	if err != nil {
		// Log error but don't try to send JSON response as headers are already sent
		fmt.Printf("Error streaming response: %v\n", err)
	}
}

// GetMetrics returns performance metrics for the standard proxy handler
func (h *ProxyHandler) GetMetrics() gin.H {
	return gin.H{
		"handler_type":     "standard_proxy",
		"optimization":     "buffer_pooling",
		"buffer_pooling":   true,
		"direct_streaming": false,
	}
}
