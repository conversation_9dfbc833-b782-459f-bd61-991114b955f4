package handlers

import (
	"net/http"
	"nova-proxy/internal/models"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

// AdminHandler handles administrative operations
type AdminHandler struct {
	balancer    *services.Balancer
	authService *services.AuthService
}

// NewAdminHandler creates a new admin handler
func NewAdminHandler(balancer *services.Balancer, authService *services.AuthService) *AdminHandler {
	return &AdminHandler{
		balancer:    balancer,
		authService: authService,
	}
}

// UpdateConfig handles dynamic configuration updates
func (h *AdminHandler) UpdateConfig(c *gin.Context) {
	var cfg map[string]models.ServiceConfig
	if err := c.BindJSO<PERSON>(&cfg); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Apply configuration updates
	for service, config := range cfg {
		h.balancer.LoadConfig(service, &config)
	}

	c.<PERSON>(200, gin.H{"message": "Config updated"})
}

// GetConfig returns the current configuration
func (h *AdminHandler) GetConfig(c *gin.Context) {
	services := h.balancer.GetAllServices()
	configs := make(map[string]*models.ServiceConfig)

	for _, service := range services {
		configs[service] = h.balancer.GetServiceConfig(service)
	}

	c.JSON(200, gin.H{
		"services": configs,
	})
}

// GetServiceStatus returns status information for all services
func (h *AdminHandler) GetServiceStatus(c *gin.Context) {
	services := h.balancer.GetAllServices()
	status := make(map[string]interface{})

	for _, service := range services {
		config := h.balancer.GetServiceConfig(service)
		if config != nil {
			// Count total upstreams across all partners
			totalUpstreams := 0
			for _, partner := range config.Partners {
				totalUpstreams += len(partner.Upstreams)
			}

			status[service] = gin.H{
				"partner_count":  len(config.Partners),
				"upstream_count": totalUpstreams,
				"partners":       config.Partners,
			}
		}
	}

	c.JSON(200, gin.H{
		"status":   "healthy",
		"services": status,
	})
}

// GetClients returns all clients (without sensitive data)
func (h *AdminHandler) GetClients(c *gin.Context) {
	clients := h.authService.ListClients()
	c.JSON(http.StatusOK, gin.H{
		"message": "Clients retrieved successfully",
		"clients": clients,
		"count":   len(clients),
	})
}

// GetClient returns information about a specific client
func (h *AdminHandler) GetClient(c *gin.Context) {
	clientName := c.Param("name")
	if clientName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Client name is required",
		})
		return
	}

	client := h.authService.GetClientInfo(clientName)
	if client == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Client not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Client retrieved successfully",
		"client":  client,
	})
}
