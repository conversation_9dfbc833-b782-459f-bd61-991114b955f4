package middleware

import (
	"net/http"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware provides authentication middleware for Gin
type AuthMiddleware struct {
	authService *services.AuthService
}

// NewAuthMiddleware creates a new authentication middleware
func NewAuthMiddleware(authService *services.AuthService) *AuthMiddleware {
	return &AuthMiddleware{
		authService: authService,
	}
}

// Authenticate middleware validates client credentials
func (m *AuthMiddleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract authentication headers
		clientName := c.GetHeader("x-auth-client")
		token := c.Get<PERSON>eader("x-auth-token")

		// Authenticate client
		authCtx, err := m.authService.Authenticate(clientName, token)
		if err != nil {
			if authError, ok := err.(*models.AuthError); ok {
				c.<PERSON>(http.StatusUnauthorized, gin.H{
					"error": gin.H{
						"code":    authError.Code,
						"message": authError.Message,
						"details": authError.Details,
					},
					"authenticated": false,
				})
			} else {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": gin.H{
						"code":    "AUTH_ERROR",
						"message": err.Error(),
					},
					"authenticated": false,
				})
			}
			c.Abort()
			return
		}

		// Store auth context in request context
		c.Set("auth_context", authCtx)
		c.Next()
	}
}

// RequireFunction middleware checks if client is authorized for a specific function
func (m *AuthMiddleware) RequireFunction(functionName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get auth context from previous middleware
		authCtxInterface, exists := c.Get("auth_context")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "NO_AUTH_CONTEXT",
					"message": "Authentication context not found",
				},
			})
			c.Abort()
			return
		}

		authCtx, ok := authCtxInterface.(*models.AuthContext)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": gin.H{
					"code":    "INVALID_AUTH_CONTEXT",
					"message": "Invalid authentication context",
				},
			})
			c.Abort()
			return
		}

		// Check authorization
		if err := m.authService.IsAuthorized(authCtx, functionName); err != nil {
			if authError, ok := err.(*models.AuthError); ok {
				c.JSON(http.StatusForbidden, gin.H{
					"error": gin.H{
						"code":    authError.Code,
						"message": authError.Message,
						"details": authError.Details,
					},
					"function": functionName,
				})
			} else {
				c.JSON(http.StatusForbidden, gin.H{
					"error": gin.H{
						"code":    "AUTHORIZATION_ERROR",
						"message": err.Error(),
					},
					"function": functionName,
				})
			}
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireService middleware checks if client is authorized for any function in a service
func (m *AuthMiddleware) RequireService() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get service from URL parameter
		service := c.Param("service")
		if service == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": gin.H{
					"code":    "MISSING_SERVICE",
					"message": "Service parameter is required",
				},
			})
			c.Abort()
			return
		}

		// Use the service name as the function name for authorization
		m.RequireFunction(service)(c)
	}
}

// GetAuthContext helper function to extract auth context from Gin context
func GetAuthContext(c *gin.Context) (*models.AuthContext, bool) {
	authCtxInterface, exists := c.Get("auth_context")
	if !exists {
		return nil, false
	}

	authCtx, ok := authCtxInterface.(*models.AuthContext)
	return authCtx, ok
}

// AddAuthInfo middleware adds authentication info to response headers
func (m *AuthMiddleware) AddAuthInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if exists && authCtx.Authenticated && authCtx.Client != nil {
			c.Header("X-Authenticated-Client", authCtx.Client.Name)
			c.Header("X-Auth-Method", authCtx.AuthMethod)
		}
		c.Next()
	}
}
