package middleware

import (
	"net/http"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

// RateLimitMiddleware handles rate limiting for requests
type RateLimitMiddleware struct {
	authService      *services.AuthService
	rateLimitService *services.RateLimitService
	functionConfigs  map[string]models.Function // function_name -> config
}

// NewRateLimitMiddleware creates a new rate limiting middleware
func NewRateLimitMiddleware(authService *services.AuthService, rateLimitService *services.RateLimitService) *RateLimitMiddleware {
	return &RateLimitMiddleware{
		authService:      authService,
		rateLimitService: rateLimitService,
		functionConfigs:  make(map[string]models.Function),
	}
}

// SetFunctionConfigs sets the function configurations for rate limiting
func (m *RateLimitMiddleware) SetFunctionConfigs(configs map[string]models.Function) {
	m.functionConfigs = configs
}

// CheckRateLimits middleware checks all applicable rate limits for a request
func (m *RateLimitMiddleware) CheckRateLimits() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract function name from URL parameter
		functionName := c.Param("service")
		if functionName == "" {
			c.Next()
			return
		}

		// Get authentication context
		authCtx, exists := GetAuthContext(c)
		if !exists {
			// If no auth context, skip rate limiting (auth middleware should handle this)
			c.Next()
			return
		}

		// 1. Check client-function specific rate limits
		if err := m.authService.CheckClientFunctionRateLimit(authCtx, functionName); err != nil {
			c.Set("rate_limit_type", "client_function")
			if authError, ok := err.(*models.AuthError); ok {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"error": gin.H{
						"code":    authError.Code,
						"message": authError.Message,
						"type":    "client_function_rate_limit",
					},
					"rate_limited": true,
				})
			} else {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"error": gin.H{
						"code":    "RATE_LIMIT_ERROR",
						"message": err.Error(),
						"type":    "client_function_rate_limit",
					},
					"rate_limited": true,
				})
			}
			c.Abort()
			return
		}

		// 2. Check function global rate limits
		if functionConfig, exists := m.functionConfigs[functionName]; exists {
			if err := m.authService.CheckFunctionRateLimit(functionName, functionConfig); err != nil {
				c.Set("rate_limit_type", "function_global")
				if authError, ok := err.(*models.AuthError); ok {
					c.JSON(http.StatusTooManyRequests, gin.H{
						"error": gin.H{
							"code":    authError.Code,
							"message": authError.Message,
							"type":    "function_rate_limit",
						},
						"rate_limited": true,
					})
				} else {
					c.JSON(http.StatusTooManyRequests, gin.H{
						"error": gin.H{
							"code":    "RATE_LIMIT_ERROR",
							"message": err.Error(),
							"type":    "function_rate_limit",
						},
						"rate_limited": true,
					})
				}
				c.Abort()
				return
			}
		}

		// Store rate limiting info in context for logging
		c.Set("rate_limit_checked", true)
		c.Set("function_name", functionName)

		c.Next()
	}
}

// CheckPartnerRateLimits middleware checks partner rate limits before forwarding requests
func (m *RateLimitMiddleware) CheckPartnerRateLimits() gin.HandlerFunc {
	return func(c *gin.Context) {
		// This middleware should be called after partner selection
		// Check if partner info is available in context
		partnerName, exists := c.Get("selected_partner_name")
		if !exists {
			c.Next()
			return
		}

		functionName := c.Param("service")
		if functionName == "" {
			c.Next()
			return
		}

		// Get partner info from context
		partnerInterface, exists := c.Get("selected_partner")
		if !exists {
			c.Next()
			return
		}

		partner, ok := partnerInterface.(*models.Partner)
		if !ok {
			c.Next()
			return
		}

		// Check partner global rate limits
		if !m.rateLimitService.CheckPartnerRateLimit(partnerName.(string), partner.GlobalRateLimit) {
			c.Set("rate_limit_type", "partner_global")
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": gin.H{
					"code":    "PARTNER_RATE_LIMIT_EXCEEDED",
					"message": "Partner rate limit exceeded",
					"type":    "partner_rate_limit",
					"partner": partnerName,
				},
				"rate_limited": true,
			})
			c.Abort()
			return
		}

		// Check partner-function specific rate limits
		for _, funcLimit := range partner.FunctionRateLimits {
			if funcLimit.FunctionName == functionName {
				if !m.rateLimitService.CheckPartnerFunctionRateLimit(partnerName.(string), functionName, funcLimit.RateLimit) {
					c.Set("rate_limit_type", "partner_function")
					c.JSON(http.StatusTooManyRequests, gin.H{
						"error": gin.H{
							"code":     "PARTNER_FUNCTION_RATE_LIMIT_EXCEEDED",
							"message":  "Partner-function rate limit exceeded",
							"type":     "partner_function_rate_limit",
							"partner":  partnerName,
							"function": functionName,
						},
						"rate_limited": true,
					})
					c.Abort()
					return
				}
				break
			}
		}

		c.Next()
	}
}

// AddRateLimitHeaders middleware adds rate limiting information to response headers
func (m *RateLimitMiddleware) AddRateLimitHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Add rate limiting headers after processing
		if rateLimitChecked, exists := c.Get("rate_limit_checked"); exists && rateLimitChecked.(bool) {
			c.Header("X-RateLimit-Checked", "true")

			if functionName, exists := c.Get("function_name"); exists {
				c.Header("X-RateLimit-Function", functionName.(string))
			}

			if partnerName, exists := c.Get("selected_partner_name"); exists {
				c.Header("X-RateLimit-Partner", partnerName.(string))
			}
		}
	}
}

// GetRateLimitStats returns current rate limiting statistics
func (m *RateLimitMiddleware) GetRateLimitStats() gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := m.rateLimitService.GetStats()
		c.JSON(http.StatusOK, gin.H{
			"message": "Rate limiting statistics retrieved successfully",
			"stats":   stats,
		})
	}
}

// ResetRateLimit allows admin to reset specific rate limiters
func (m *RateLimitMiddleware) ResetRateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		var request struct {
			LimiterType string `json:"limiter_type" binding:"required"` // client, client-function, function, partner, partner-function
			Key         string `json:"key" binding:"required"`          // identifier for the limiter
		}

		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid request format",
				"details": err.Error(),
			})
			return
		}

		m.rateLimitService.ResetLimiter(request.LimiterType, request.Key)

		c.JSON(http.StatusOK, gin.H{
			"message":      "Rate limiter reset successfully",
			"limiter_type": request.LimiterType,
			"key":          request.Key,
		})
	}
}
