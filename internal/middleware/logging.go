package middleware

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"
	"nova-proxy/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// responseWriter wraps gin.ResponseWriter to capture response data efficiently
type responseWriter struct {
	gin.ResponseWriter
	body          *bytes.Buffer
	statusCode    int
	totalSize     int64
	maxBodySize   int64
	bodyTruncated bool
}

func newResponseWriter(w gin.ResponseWriter, maxBodySize int64) *responseWriter {
	// Use buffer pool for response body buffering
	body := utils.BufferPool32K.Get()
	return &responseWriter{
		ResponseWriter: w,
		body:           body,
		statusCode:     200,
		maxBodySize:    maxBodySize,
	}
}

func (rw *responseWriter) Write(b []byte) (int, error) {
	rw.totalSize += int64(len(b))

	// Only buffer response if it's within size limits
	if rw.body.Len() < int(rw.maxBodySize) && !rw.bodyTruncated {
		remainingSpace := int(rw.maxBodySize) - rw.body.Len()
		if len(b) <= remainingSpace {
			rw.body.Write(b)
		} else {
			// Write what we can and mark as truncated
			rw.body.Write(b[:remainingSpace])
			rw.bodyTruncated = true
		}
	}

	return rw.ResponseWriter.Write(b)
}

func (rw *responseWriter) WriteHeader(statusCode int) {
	rw.statusCode = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

// cleanup returns the buffer to the pool
func (rw *responseWriter) cleanup() {
	if rw.body != nil {
		utils.BufferPool32K.Put(rw.body)
		rw.body = nil
	}
}

// LoggingMiddleware creates a Gin middleware for request/response logging
func LoggingMiddleware(logger *services.LoggerService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip logging if not enabled
		if logger == nil {
			c.Next()
			return
		}

		startTime := time.Now()
		requestID := generateRequestID(c)

		// Set request ID in context for use by handlers
		c.Set("request_id", requestID)

		// Capture request data with size-aware reading
		var requestBody []byte
		var requestBodySize int64

		if c.Request.Body != nil {
			// Get content length from header first
			if contentLength := c.GetHeader("Content-Length"); contentLength != "" {
				if size, err := strconv.ParseInt(contentLength, 10, 64); err == nil {
					requestBodySize = size
				}
			}

			// Only read body if it's reasonably sized for logging
			maxBodyRead := int64(10 * 1024 * 1024) // 10MB limit for reading
			if requestBodySize == 0 || requestBodySize <= maxBodyRead {
				requestBody, _ = io.ReadAll(c.Request.Body)
				if requestBodySize == 0 {
					requestBodySize = int64(len(requestBody))
				}
				// Restore the body for the handler to use
				c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
			} else {
				// For very large bodies, skip reading content but preserve the stream
				c.Request.Body = io.NopCloser(c.Request.Body)
			}
		}

		// Create response writer wrapper with size limits
		maxResponseBodySize := int64(1024 * 1024) // 1MB limit for response body logging
		rw := newResponseWriter(c.Writer, maxResponseBodySize)
		c.Writer = rw

		// Log request entry (both traditional and structured)
		go func() {
			requestEntry := createRequestLogEntry(c, requestID, requestBody, startTime)
			logger.LogRequest(requestEntry)
			logger.LogRequestStructured(requestEntry)
		}()

		// Process the request
		c.Next()

		// Log response entry (both traditional and structured)
		go func() {
			responseEntry := createResponseLogEntry(c, rw, requestID, startTime)
			logger.LogResponse(responseEntry)
			logger.LogResponseStructured(responseEntry)
			// Clean up the response writer buffer after logging
			rw.cleanup()
		}()

		// Log proxy event if this is a proxy request (both traditional and structured)
		if service := c.Param("service"); service != "" {
			go func() {
				proxyEntry := createProxyLogEntry(c, requestID, startTime, service)
				logger.LogProxyEvent(proxyEntry)
				logger.LogProxyEventStructured(proxyEntry)
			}()
		}
	}
}

// generateRequestID creates a unique request ID
func generateRequestID(c *gin.Context) string {
	// Try to get existing request ID from headers
	if reqID := c.GetHeader("X-Request-ID"); reqID != "" {
		return reqID
	}

	// Generate new UUID-based request ID
	service := c.Param("service")
	if service == "" {
		service = "admin"
	}

	return fmt.Sprintf("%s-%s", service, uuid.New().String()[:8])
}

// createRequestLogEntry creates a request log entry from the Gin context
func createRequestLogEntry(c *gin.Context, requestID string, body []byte, startTime time.Time) *models.RequestLogEntry {
	// Extract headers if configured
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0] // Take first value
		}
	}

	// Process body based on content type
	bodyStr := ""
	bodySize := int64(len(body))

	if len(body) > 0 {
		contentType := c.GetHeader("Content-Type")

		// Check if it's likely binary content
		if utils.IsBinaryContent(body) {
			bodyStr = fmt.Sprintf("[BINARY_CONTENT:%d bytes]", len(body))
		} else {
			// For text content, truncate if too large
			maxBodySample := 1000 // default max sample size
			if len(body) > maxBodySample {
				bodyStr = string(body[:maxBodySample]) + "... [TRUNCATED]"
			} else {
				bodyStr = string(body)
			}
		}

		// Special handling for multipart forms
		if strings.Contains(contentType, "multipart/form-data") {
			bodyStr = fmt.Sprintf("[MULTIPART_FORM:%d bytes]", len(body))
		}
	}

	return &models.RequestLogEntry{
		ID:         uuid.New().String(),
		Timestamp:  startTime,
		Method:     c.Request.Method,
		URL:        c.Request.URL.String(),
		Headers:    headers,
		Body:       bodyStr,
		BodySize:   bodySize,
		RemoteAddr: c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Service:    c.Param("service"),
		RequestID:  requestID,
	}
}

// createResponseLogEntry creates a response log entry
func createResponseLogEntry(c *gin.Context, rw *responseWriter, requestID string, startTime time.Time) *models.ResponseLogEntry {
	// Extract response headers
	headers := make(map[string]string)
	for key, values := range rw.Header() {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// Process response body with efficient size tracking
	responseBody := rw.body.Bytes()
	bodyStr := ""
	bodySize := rw.totalSize // Use actual total size, not just buffered size

	if len(responseBody) > 0 {
		// Check if it's binary content
		if utils.IsBinaryContent(responseBody) {
			if rw.bodyTruncated {
				bodyStr = fmt.Sprintf("[BINARY_CONTENT:%d bytes, TRUNCATED from %d bytes]", len(responseBody), bodySize)
			} else {
				bodyStr = fmt.Sprintf("[BINARY_CONTENT:%d bytes]", len(responseBody))
			}
		} else {
			// For text content, show what we have
			maxBodySample := 1000 // default max sample size
			if len(responseBody) > maxBodySample {
				bodyStr = string(responseBody[:maxBodySample]) + "... [TRUNCATED]"
			} else {
				bodyStr = string(responseBody)
				if rw.bodyTruncated {
					bodyStr += fmt.Sprintf("... [TRUNCATED from %d bytes]", bodySize)
				}
			}
		}
	} else if bodySize > 0 {
		// We have size info but no body content (very large response)
		bodyStr = fmt.Sprintf("[LARGE_RESPONSE:%d bytes, not logged]", bodySize)
	}

	// Get error information if any
	errorStr := ""
	if len(c.Errors) > 0 {
		errorStr = c.Errors.String()
	}

	// Get upstream information from context if available
	upstreamURL := ""
	partnerName := ""
	if upstream, exists := c.Get("upstream_url"); exists {
		if url, ok := upstream.(string); ok {
			upstreamURL = url
		}
	}
	if partner, exists := c.Get("partner_name"); exists {
		if name, ok := partner.(string); ok {
			partnerName = name
		}
	}

	return &models.ResponseLogEntry{
		ID:          uuid.New().String(),
		RequestID:   requestID,
		Timestamp:   time.Now(),
		StatusCode:  rw.statusCode,
		Headers:     headers,
		Body:        bodyStr,
		BodySize:    bodySize,
		Duration:    time.Since(startTime),
		UpstreamURL: upstreamURL,
		PartnerName: partnerName,
		Service:     c.Param("service"),
		Error:       errorStr,
	}
}

// createProxyLogEntry creates a proxy event log entry
func createProxyLogEntry(c *gin.Context, requestID string, startTime time.Time, service string) *models.ProxyLogEntry {
	// Get routing information from context
	partnerName := c.GetHeader("x-partner")
	businessCode := c.GetHeader("x-biz")

	// Determine routing reason
	routingReason := "weighted_round_robin" // default
	if partnerName != "" {
		routingReason = "partner_header"
	} else if businessCode != "" {
		routingReason = "business_code"
	}

	// Get upstream information from context if available
	upstreamURL := ""
	if upstream, exists := c.Get("upstream_url"); exists {
		if url, ok := upstream.(string); ok {
			upstreamURL = url
		}
	}

	// Get debug mode information
	debugMode := false
	if debug, exists := c.Get("debug_mode"); exists {
		if d, ok := debug.(bool); ok {
			debugMode = d
		}
	}

	// Create metadata
	metadata := map[string]interface{}{
		"method":       c.Request.Method,
		"user_agent":   c.GetHeader("User-Agent"),
		"remote_addr":  c.ClientIP(),
		"content_type": c.GetHeader("Content-Type"),
	}

	if partnerName != "" {
		metadata["partner_header"] = partnerName
	}
	if businessCode != "" {
		metadata["business_code"] = businessCode
	}

	return &models.ProxyLogEntry{
		ID:            uuid.New().String(),
		RequestID:     requestID,
		Timestamp:     startTime,
		Event:         "request_processed",
		Service:       service,
		PartnerName:   partnerName,
		UpstreamURL:   upstreamURL,
		Duration:      time.Since(startTime),
		Error:         "",
		Metadata:      metadata,
		DebugMode:     debugMode,
		RoutingReason: routingReason,
	}
}
