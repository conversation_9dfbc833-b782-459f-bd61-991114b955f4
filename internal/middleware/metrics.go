package middleware

import (
	"fmt"
	"slices"
	"time"

	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

// MetricsMiddleware provides Prometheus metrics collection for Gin
type MetricsMiddleware struct {
	metricsService *services.MetricsService
}

// NewMetricsMiddleware creates a new metrics middleware
func NewMetricsMiddleware(metricsService *services.MetricsService) *MetricsMiddleware {
	return &MetricsMiddleware{
		metricsService: metricsService,
	}
}

// CollectMetrics middleware collects basic HTTP metrics
func (m *MetricsMiddleware) CollectMetrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Record start time and increment in-flight counter
		startTime := time.Now()
		m.metricsService.IncRequestsInFlight()

		// Process the request
		c.Next()

		// Record end time and decrement in-flight counter
		duration := time.Since(startTime)
		m.metricsService.DecRequestsInFlight()

		// Extract metrics data
		method := c.Request.Method
		endpoint := getEndpointPattern(c)
		statusCode := c.Writer.Status()
		requestSize := getRequestSize(c)
		responseSize := int64(0) // We'll get this from the logging middleware if available

		// Try to get response size from context if logging middleware set it
		if size, exists := c.Get("response_size"); exists {
			if sizeInt, ok := size.(int); ok {
				responseSize = int64(sizeInt)
			}
		}

		// Skip metrics recording for excluded paths
		if shouldExcludeFromMetrics(endpoint) {
			return
		}

		// Record basic request metrics
		m.metricsService.RecordRequest(method, endpoint, statusCode, duration, requestSize, responseSize)

		// Record service-specific metrics if this is a service request
		if service := c.Param("service"); service != "" {
			m.recordServiceMetrics(c, service, duration, statusCode)
		}
	}
}

// CollectAuthMetrics middleware collects authentication metrics
func (m *MetricsMiddleware) CollectAuthMetrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract client name
		clientName := c.GetHeader("x-auth-client")
		if clientName == "" {
			clientName = "unknown"
		}

		// Process the request
		c.Next()

		// Determine authentication result
		statusCode := c.Writer.Status()
		var result string
		var reason string

		switch statusCode {
		case 200:
			result = "success"
		case 401:
			result = "failure"
			reason = "unauthorized"
		case 403:
			result = "failure"
			reason = "forbidden"
		case 429:
			result = "failure"
			reason = "rate_limited"
		default:
			result = "unknown"
			reason = "other"
		}

		// Record authentication metrics
		m.metricsService.RecordAuthRequest(clientName, result)
		if result == "failure" {
			m.metricsService.RecordAuthFailure(clientName, reason)
		}
	}
}

func (m *MetricsMiddleware) AddLatencyHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Record start time for total latency
		totalStartTime := time.Now()

		// Process the request
		c.Next()

		// Calculate total latency
		totalLatency := time.Since(totalStartTime)

		// Get proxy and upstream latencies from context if available
		var proxyLatency, upstreamLatency time.Duration

		if proxyDuration, exists := c.Get("proxy_latency"); exists {
			if duration, ok := proxyDuration.(time.Duration); ok {
				proxyLatency = duration
			}
		} else {
			c.Header("X-Debug-Proxy-Latency", "not-found")
		}

		if upstreamDuration, exists := c.Get("upstream_latency"); exists {
			if duration, ok := upstreamDuration.(time.Duration); ok {
				upstreamLatency = duration
			}
		} else {
			c.Header("X-Debug-Upstream-Latency", "not-found")
		}

		// Add latency headers to response using Writer.Header() to ensure they're set
		c.Writer.Header().Set("X-Proxy-Latency-Ms", fmt.Sprintf("%.2f", proxyLatency.Seconds()*1000))
		c.Writer.Header().Set("X-Upstream-Latency-Ms", fmt.Sprintf("%.2f", upstreamLatency.Seconds()*1000))
		c.Writer.Header().Set("X-Total-Latency-Ms", fmt.Sprintf("%.2f", totalLatency.Seconds()*1000))

		// Add debug headers to verify middleware is working
		c.Writer.Header().Set("X-Latency-Debug", "enabled")
		c.Writer.Header().Set("X-Test-Static", "123.45")

		// Add additional timing breakdown if available
		if authDuration, exists := c.Get("auth_latency"); exists {
			if duration, ok := authDuration.(time.Duration); ok {
				c.Header("X-Auth-Latency-Ms", fmt.Sprintf("%.2f", duration.Seconds()*1000))
			}
		}

		if rateLimitDuration, exists := c.Get("ratelimit_latency"); exists {
			if duration, ok := rateLimitDuration.(time.Duration); ok {
				c.Header("X-RateLimit-Latency-Ms", fmt.Sprintf("%.2f", duration.Seconds()*1000))
			}
		}

		if routingDuration, exists := c.Get("routing_latency"); exists {
			if duration, ok := routingDuration.(time.Duration); ok {
				c.Header("X-Routing-Latency-Ms", fmt.Sprintf("%.2f", duration.Seconds()*1000))
			}
		}

		// Add partner and service info for debugging
		if partner, exists := c.Get("selected_partner_name"); exists {
			if partnerName, ok := partner.(string); ok {
				c.Header("X-Selected-Partner", partnerName)
			}
		}

		if service := c.Param("service"); service != "" {
			c.Header("X-Service", service)
		}

		// Add handler type info
		if handlerType, exists := c.Get("handler_type"); exists {
			if ht, ok := handlerType.(string); ok {
				c.Header("X-Handler-Type", ht)
			}
		}
	}
}

// CollectRateLimitMetrics middleware collects rate limiting metrics
func (m *MetricsMiddleware) CollectRateLimitMetrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract client and function information
		clientName := c.GetHeader("x-auth-client")
		if clientName == "" {
			clientName = "unknown"
		}

		functionName := c.Param("service")
		if functionName == "" {
			functionName = "unknown"
		}

		// Process the request
		c.Next()

		// Determine rate limit result
		statusCode := c.Writer.Status()
		var result string
		var limitType string

		if statusCode == 429 {
			result = "hit"
			// Try to determine the type of rate limit from response
			if rateLimitType, exists := c.Get("rate_limit_type"); exists {
				limitType = rateLimitType.(string)
			} else {
				limitType = "unknown"
			}
			m.metricsService.RecordRateLimitHit(clientName, functionName, limitType)
		} else {
			result = "pass"
		}

		// Record rate limit check
		m.metricsService.RecordRateLimitCheck(clientName, functionName, result)
	}
}

// recordServiceMetrics records service-specific metrics
func (m *MetricsMiddleware) recordServiceMetrics(c *gin.Context, service string, duration time.Duration, statusCode int) {
	// Extract client information
	clientName := c.GetHeader("x-auth-client")
	if clientName == "" {
		clientName = "unknown"
	}

	// Extract partner information
	partnerName := "unknown"
	if partner, exists := c.Get("selected_partner_name"); exists {
		partnerName = partner.(string)
	}

	// Extract business code
	businessCode := c.GetHeader("x-biz")
	if businessCode == "" {
		businessCode = "default"
	}

	// Record service metrics
	m.metricsService.RecordServiceRequest(service, clientName, partnerName, duration)
	m.metricsService.RecordClientRequest(clientName, service, statusCode)

	// Record business code metrics
	m.metricsService.RecordBusinessCodeRequest(businessCode, service, partnerName)

	// Record upstream metrics if available
	if upstreamURL, exists := c.Get("upstream_url"); exists && upstreamURL != "" {
		success := statusCode >= 200 && statusCode < 400
		m.metricsService.RecordUpstreamRequest(partnerName, service, statusCode, duration, success)
	}

	// Record latency metrics if available
	if handlerType, exists := c.Get("handler_type"); exists {
		if ht, ok := handlerType.(string); ok {
			// Record proxy latency
			if proxyLatency, exists := c.Get("proxy_latency"); exists {
				if pl, ok := proxyLatency.(time.Duration); ok {
					m.metricsService.RecordProxyLatency(service, partnerName, ht, pl)
				}
			}

			// Record upstream latency
			if upstreamLatency, exists := c.Get("upstream_latency"); exists {
				if ul, ok := upstreamLatency.(time.Duration); ok {
					if upstreamURL, exists := c.Get("upstream_url"); exists {
						if url, ok := upstreamURL.(string); ok {
							m.metricsService.RecordUpstreamLatency(service, partnerName, url, ul)
						}
					}
				}
			}
		}
	}

	// Record service errors for non-2xx responses
	if statusCode >= 400 {
		errorType := getErrorType(statusCode)
		m.metricsService.RecordServiceError(service, partnerName, errorType)
	}
}

// getEndpointPattern returns a normalized endpoint pattern for metrics
func getEndpointPattern(c *gin.Context) string {
	// Get the route pattern instead of the actual path
	if route := c.FullPath(); route != "" {
		return route
	}

	// Fallback to request path
	path := c.Request.URL.Path

	// Normalize common patterns
	if len(path) > 1 && path[0] == '/' {
		segments := []rune(path[1:])
		if len(segments) > 0 {
			// Check if this looks like a service endpoint
			if c.Param("service") != "" {
				return "/:service"
			}
			// Check if this is an admin endpoint
			if len(path) > 6 && path[:6] == "/admin" {
				return "/admin/*"
			}
		}
	}

	return path
}

// shouldExcludeFromMetrics checks if the endpoint should be excluded from metrics
func shouldExcludeFromMetrics(endpoint string) bool {
	excludedPaths := []string{
		"/metrics",
		"/health",
		"/liveness",
		"/readiness",
	}

	return slices.Contains(excludedPaths, endpoint)
}

// getRequestSize estimates the request size
func getRequestSize(c *gin.Context) int64 {
	if c.Request.ContentLength > 0 {
		return c.Request.ContentLength
	}

	// Estimate based on headers and URL
	size := int64(len(c.Request.URL.String()))
	for name, values := range c.Request.Header {
		size += int64(len(name))
		for _, value := range values {
			size += int64(len(value))
		}
	}

	return size
}

// getErrorType categorizes HTTP status codes into error types
func getErrorType(statusCode int) string {
	switch {
	case statusCode >= 400 && statusCode < 500:
		switch statusCode {
		case 400:
			return "bad_request"
		case 401:
			return "unauthorized"
		case 403:
			return "forbidden"
		case 404:
			return "not_found"
		case 429:
			return "rate_limited"
		default:
			return "client_error"
		}
	case statusCode >= 500:
		switch statusCode {
		case 500:
			return "internal_error"
		case 502:
			return "bad_gateway"
		case 503:
			return "service_unavailable"
		case 504:
			return "gateway_timeout"
		default:
			return "server_error"
		}
	default:
		return "unknown"
	}
}

// InjectMetricsContext middleware injects metrics context for downstream middleware
func (m *MetricsMiddleware) InjectMetricsContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Store metrics service in context for other middleware to use
		c.Set("metrics_service", m.metricsService)
		c.Next()
	}
}
