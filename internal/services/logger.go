package services

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"nova-proxy/internal/models"

	"github.com/sirupsen/logrus"
)

// LogEntry represents a generic log entry that can be any of the specific log types
type LogEntry struct {
	Type string      `json:"type"` // "request", "response", "proxy"
	Data interface{} `json:"data"`
}

// LoggerService handles asynchronous logging with background goroutines
type LoggerService struct {
	config         *models.LogConfig
	requestChan    chan *models.RequestLogEntry
	responseChan   chan *models.ResponseLogEntry
	proxyEventChan chan *models.ProxyLogEntry
	stopChan       chan struct{}
	wg             sync.WaitGroup
	mu             sync.RWMutex
	currentFiles   map[string]*os.File // map of log type to current file
	fileSizes      map[string]int64    // track current file sizes
	ctx            context.Context
	cancel         context.CancelFunc
	logger         *logrus.Logger // Structured logger instance
	requestLogger  *logrus.Logger // Dedicated request logger
	responseLogger *logrus.Logger // Dedicated response logger
	proxyLogger    *logrus.Logger // Dedicated proxy event logger
}

// NewLoggerService creates a new logger service instance
func NewLoggerService(config *models.LogConfig) *LoggerService {
	ctx, cancel := context.WithCancel(context.Background())

	ls := &LoggerService{
		config:         config,
		requestChan:    make(chan *models.RequestLogEntry, config.BufferSize),
		responseChan:   make(chan *models.ResponseLogEntry, config.BufferSize),
		proxyEventChan: make(chan *models.ProxyLogEntry, config.BufferSize),
		stopChan:       make(chan struct{}),
		currentFiles:   make(map[string]*os.File),
		fileSizes:      make(map[string]int64),
		ctx:            ctx,
		cancel:         cancel,
	}

	// Create log directory if it doesn't exist
	if err := os.MkdirAll(config.Directory, 0755); err != nil {
		fmt.Printf("Failed to create log directory: %v\n", err)
		return ls
	}

	// Initialize structured loggers
	ls.initializeLoggers()

	// Start background workers
	ls.startWorkers()

	return ls
}

// initializeLoggers sets up structured loggers with appropriate configurations
func (ls *LoggerService) initializeLoggers() {
	// Main application logger
	ls.logger = logrus.New()
	ls.setupLogger(ls.logger, "application")

	// Request logger
	ls.requestLogger = logrus.New()
	ls.setupLogger(ls.requestLogger, "requests")

	// Response logger
	ls.responseLogger = logrus.New()
	ls.setupLogger(ls.responseLogger, "responses")

	// Proxy event logger
	ls.proxyLogger = logrus.New()
	ls.setupLogger(ls.proxyLogger, "proxy_events")
}

// setupLogger configures a logrus logger with appropriate settings
func (ls *LoggerService) setupLogger(logger *logrus.Logger, logType string) {
	// Set log level based on configuration
	switch ls.config.Level {
	case models.LogLevelDebug:
		logger.SetLevel(logrus.DebugLevel)
	case models.LogLevelInfo:
		logger.SetLevel(logrus.InfoLevel)
	case models.LogLevelWarn:
		logger.SetLevel(logrus.WarnLevel)
	case models.LogLevelError:
		logger.SetLevel(logrus.ErrorLevel)
	default:
		logger.SetLevel(logrus.InfoLevel)
	}

	// Set JSON formatter for structured logging
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339Nano,
		FieldMap: logrus.FieldMap{
			logrus.FieldKeyTime:  "timestamp",
			logrus.FieldKeyLevel: "level",
			logrus.FieldKeyMsg:   "message",
		},
	})

	// Create log file for this logger type
	if ls.config.Enabled {
		logFile := filepath.Join(ls.config.Directory, fmt.Sprintf("%s.log", logType))
		file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			fmt.Printf("Failed to open log file %s: %v\n", logFile, err)
			logger.SetOutput(os.Stdout)
		} else {
			logger.SetOutput(file)
		}
	} else {
		logger.SetOutput(os.Stdout)
	}
}

// startWorkers starts the background goroutines for processing logs
func (ls *LoggerService) startWorkers() {
	if !ls.config.Enabled {
		return
	}

	// Start request log worker
	if ls.config.LogRequests {
		ls.wg.Add(1)
		go ls.requestLogWorker()
	}

	// Start response log worker
	if ls.config.LogResponses {
		ls.wg.Add(1)
		go ls.responseLogWorker()
	}

	// Start proxy event log worker
	if ls.config.LogProxyEvents {
		ls.wg.Add(1)
		go ls.proxyEventLogWorker()
	}

	// Start periodic flush worker
	ls.wg.Add(1)
	go ls.flushWorker()
}

// LogRequest logs a request entry asynchronously
func (ls *LoggerService) LogRequest(entry *models.RequestLogEntry) {
	if !ls.config.Enabled || !ls.config.LogRequests {
		return
	}

	select {
	case ls.requestChan <- entry:
		// Successfully queued
	default:
		// Channel is full, drop the log entry to avoid blocking
		fmt.Printf("Request log channel full, dropping log entry\n")
	}
}

// LogResponse logs a response entry asynchronously
func (ls *LoggerService) LogResponse(entry *models.ResponseLogEntry) {
	if !ls.config.Enabled || !ls.config.LogResponses {
		return
	}

	select {
	case ls.responseChan <- entry:
		// Successfully queued
	default:
		// Channel is full, drop the log entry to avoid blocking
		fmt.Printf("Response log channel full, dropping log entry\n")
	}
}

// LogProxyEvent logs a proxy event entry asynchronously
func (ls *LoggerService) LogProxyEvent(entry *models.ProxyLogEntry) {
	if !ls.config.Enabled || !ls.config.LogProxyEvents {
		return
	}

	select {
	case ls.proxyEventChan <- entry:
		// Successfully queued
	default:
		// Channel is full, drop the log entry to avoid blocking
		fmt.Printf("Proxy event log channel full, dropping log entry\n")
	}
}

// Structured logging methods using Logrus

// LogInfo logs an informational message with structured fields
func (ls *LoggerService) LogInfo(message string, fields logrus.Fields) {
	if !ls.config.Enabled {
		return
	}
	ls.logger.WithFields(fields).Info(message)
}

// LogError logs an error message with structured fields
func (ls *LoggerService) LogError(message string, err error, fields logrus.Fields) {
	if !ls.config.Enabled {
		return
	}
	if fields == nil {
		fields = logrus.Fields{}
	}
	if err != nil {
		fields["error"] = err.Error()
	}
	ls.logger.WithFields(fields).Error(message)
}

// LogWarn logs a warning message with structured fields
func (ls *LoggerService) LogWarn(message string, fields logrus.Fields) {
	if !ls.config.Enabled {
		return
	}
	ls.logger.WithFields(fields).Warn(message)
}

// LogDebug logs a debug message with structured fields
func (ls *LoggerService) LogDebug(message string, fields logrus.Fields) {
	if !ls.config.Enabled {
		return
	}
	ls.logger.WithFields(fields).Debug(message)
}

// LogRequestStructured logs a request with structured fields using Logrus
func (ls *LoggerService) LogRequestStructured(entry *models.RequestLogEntry) {
	if !ls.config.Enabled || !ls.config.LogRequests {
		return
	}

	fields := logrus.Fields{
		"request_id":  entry.RequestID,
		"method":      entry.Method,
		"url":         entry.URL,
		"remote_addr": entry.RemoteAddr,
		"user_agent":  entry.UserAgent,
		"service":     entry.Service,
		"body_size":   entry.BodySize,
	}

	if ls.config.IncludeHeaders && len(entry.Headers) > 0 {
		fields["headers"] = entry.Headers
	}

	if ls.config.IncludeBody && entry.Body != "" {
		fields["body"] = entry.Body
	}

	ls.requestLogger.WithFields(fields).Info("HTTP Request")
}

// LogResponseStructured logs a response with structured fields using Logrus
func (ls *LoggerService) LogResponseStructured(entry *models.ResponseLogEntry) {
	if !ls.config.Enabled || !ls.config.LogResponses {
		return
	}

	fields := logrus.Fields{
		"request_id":   entry.RequestID,
		"status_code":  entry.StatusCode,
		"duration_ms":  entry.Duration.Milliseconds(),
		"body_size":    entry.BodySize,
		"upstream_url": entry.UpstreamURL,
		"partner_name": entry.PartnerName,
		"service":      entry.Service,
	}

	if ls.config.IncludeHeaders && len(entry.Headers) > 0 {
		fields["headers"] = entry.Headers
	}

	if ls.config.IncludeBody && entry.Body != "" {
		fields["body"] = entry.Body
	}

	if entry.Error != "" {
		fields["error"] = entry.Error
	}

	level := logrus.InfoLevel
	if entry.StatusCode >= 400 {
		level = logrus.WarnLevel
	}
	if entry.StatusCode >= 500 {
		level = logrus.ErrorLevel
	}

	ls.responseLogger.WithFields(fields).Log(level, "HTTP Response")
}

// LogProxyEventStructured logs a proxy event with structured fields using Logrus
func (ls *LoggerService) LogProxyEventStructured(entry *models.ProxyLogEntry) {
	if !ls.config.Enabled || !ls.config.LogProxyEvents {
		return
	}

	fields := logrus.Fields{
		"request_id":     entry.RequestID,
		"event":          entry.Event,
		"service":        entry.Service,
		"partner_name":   entry.PartnerName,
		"upstream_url":   entry.UpstreamURL,
		"duration_ms":    entry.Duration.Milliseconds(),
		"debug_mode":     entry.DebugMode,
		"routing_reason": entry.RoutingReason,
	}

	if len(entry.Metadata) > 0 {
		fields["metadata"] = entry.Metadata
	}

	if entry.Error != "" {
		fields["error"] = entry.Error
		ls.proxyLogger.WithFields(fields).Error("Proxy Event")
	} else {
		ls.proxyLogger.WithFields(fields).Info("Proxy Event")
	}
}

// requestLogWorker processes request log entries in the background
func (ls *LoggerService) requestLogWorker() {
	defer ls.wg.Done()

	for {
		select {
		case <-ls.ctx.Done():
			return
		case entry := <-ls.requestChan:
			ls.writeLogEntry("requests", entry)
		}
	}
}

// responseLogWorker processes response log entries in the background
func (ls *LoggerService) responseLogWorker() {
	defer ls.wg.Done()

	for {
		select {
		case <-ls.ctx.Done():
			return
		case entry := <-ls.responseChan:
			ls.writeLogEntry("responses", entry)
		}
	}
}

// proxyEventLogWorker processes proxy event log entries in the background
func (ls *LoggerService) proxyEventLogWorker() {
	defer ls.wg.Done()

	for {
		select {
		case <-ls.ctx.Done():
			return
		case entry := <-ls.proxyEventChan:
			ls.writeLogEntry("proxy_events", entry)
		}
	}
}

// flushWorker periodically flushes log files to disk
func (ls *LoggerService) flushWorker() {
	defer ls.wg.Done()

	ticker := time.NewTicker(ls.config.FlushInterval.ToDuration())
	defer ticker.Stop()

	for {
		select {
		case <-ls.ctx.Done():
			ls.flushAllFiles()
			return
		case <-ticker.C:
			ls.flushAllFiles()
		}
	}
}

// writeLogEntry writes a log entry to the appropriate file
func (ls *LoggerService) writeLogEntry(logType string, entry interface{}) {
	ls.mu.Lock()
	defer ls.mu.Unlock()

	// Get or create log file for this type
	file, err := ls.getOrCreateLogFile(logType)
	if err != nil {
		fmt.Printf("Failed to get log file for %s: %v\n", logType, err)
		return
	}

	// Marshal entry to JSON
	jsonData, err := json.Marshal(entry)
	if err != nil {
		fmt.Printf("Failed to marshal log entry: %v\n", err)
		return
	}

	// Write to file with newline
	data := append(jsonData, '\n')
	n, err := file.Write(data)
	if err != nil {
		fmt.Printf("Failed to write log entry: %v\n", err)
		return
	}

	// Update file size tracking
	ls.fileSizes[logType] += int64(n)

	// Check if rotation is needed
	if ls.fileSizes[logType] >= ls.config.MaxFileSize {
		ls.rotateLogFile(logType)
	}
}

// getOrCreateLogFile gets the current log file for a type or creates a new one
func (ls *LoggerService) getOrCreateLogFile(logType string) (*os.File, error) {
	if file, exists := ls.currentFiles[logType]; exists {
		return file, nil
	}

	// Create new log file
	filename := fmt.Sprintf("%s_%s.log", logType, time.Now().Format("2006-01-02_15-04-05"))
	filepath := filepath.Join(ls.config.Directory, filename)

	file, err := os.OpenFile(filepath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}

	ls.currentFiles[logType] = file
	ls.fileSizes[logType] = 0

	// Get current file size if file already exists
	if stat, err := file.Stat(); err == nil {
		ls.fileSizes[logType] = stat.Size()
	}

	return file, nil
}

// rotateLogFile rotates the log file when it reaches max size
func (ls *LoggerService) rotateLogFile(logType string) {
	// Close current file
	if file, exists := ls.currentFiles[logType]; exists {
		file.Close()
		delete(ls.currentFiles, logType)
	}

	// Compress old log if configured
	if ls.config.CompressOldLogs {
		go ls.compressOldLogs(logType)
	}

	// Clean up old files
	go ls.cleanupOldFiles(logType)

	// Reset file size
	ls.fileSizes[logType] = 0
}

// flushAllFiles flushes all open log files to disk
func (ls *LoggerService) flushAllFiles() {
	ls.mu.RLock()
	defer ls.mu.RUnlock()

	for _, file := range ls.currentFiles {
		if file != nil {
			file.Sync()
		}
	}
}

// compressOldLogs compresses old log files to save space
func (ls *LoggerService) compressOldLogs(logType string) {
	pattern := filepath.Join(ls.config.Directory, fmt.Sprintf("%s_*.log", logType))
	files, err := filepath.Glob(pattern)
	if err != nil {
		return
	}

	for _, file := range files {
		// Skip current log file
		if ls.isCurrentLogFile(file, logType) {
			continue
		}

		// Check if already compressed
		if strings.HasSuffix(file, ".gz") {
			continue
		}

		// Compress the file
		if err := ls.compressFile(file); err != nil {
			fmt.Printf("Failed to compress log file %s: %v\n", file, err)
		}
	}
}

// compressFile compresses a single file using gzip
func (ls *LoggerService) compressFile(filename string) error {
	// Open source file
	src, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer src.Close()

	// Create compressed file
	dst, err := os.Create(filename + ".gz")
	if err != nil {
		return err
	}
	defer dst.Close()

	// Create gzip writer
	gw := gzip.NewWriter(dst)
	defer gw.Close()

	// Copy data
	_, err = io.Copy(gw, src)
	if err != nil {
		return err
	}

	// Remove original file
	return os.Remove(filename)
}

// isCurrentLogFile checks if a file is the current log file for a type
func (ls *LoggerService) isCurrentLogFile(filename, logType string) bool {
	ls.mu.RLock()
	defer ls.mu.RUnlock()

	if file, exists := ls.currentFiles[logType]; exists {
		if stat, err := file.Stat(); err == nil {
			return filepath.Base(filename) == stat.Name()
		}
	}
	return false
}

// cleanupOldFiles removes old log files beyond the configured limit
func (ls *LoggerService) cleanupOldFiles(logType string) {
	pattern := filepath.Join(ls.config.Directory, fmt.Sprintf("%s_*", logType))
	files, err := filepath.Glob(pattern)
	if err != nil {
		return
	}

	// Sort files by modification time (newest first)
	sort.Slice(files, func(i, j int) bool {
		statI, errI := os.Stat(files[i])
		statJ, errJ := os.Stat(files[j])
		if errI != nil || errJ != nil {
			return false
		}
		return statI.ModTime().After(statJ.ModTime())
	})

	// Remove files beyond the limit
	if len(files) > ls.config.MaxFiles {
		for _, file := range files[ls.config.MaxFiles:] {
			if err := os.Remove(file); err != nil {
				fmt.Printf("Failed to remove old log file %s: %v\n", file, err)
			}
		}
	}
}

// Shutdown gracefully shuts down the logger service
func (ls *LoggerService) Shutdown() {
	// Cancel context to stop workers
	ls.cancel()

	// Wait for all workers to finish
	ls.wg.Wait()

	// Close all open files
	ls.mu.Lock()
	defer ls.mu.Unlock()

	for _, file := range ls.currentFiles {
		if file != nil {
			file.Close()
		}
	}
}

// GetStats returns logging statistics
func (ls *LoggerService) GetStats() map[string]interface{} {
	ls.mu.RLock()
	defer ls.mu.RUnlock()

	stats := map[string]interface{}{
		"enabled":             ls.config.Enabled,
		"request_queue_size":  len(ls.requestChan),
		"response_queue_size": len(ls.responseChan),
		"proxy_queue_size":    len(ls.proxyEventChan),
		"open_files":          len(ls.currentFiles),
		"file_sizes":          ls.fileSizes,
	}

	return stats
}
