package services

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"nova-proxy/internal/models"
)

// AuthService handles client authentication and authorization
type AuthService struct {
	config           *models.AuthConfig
	mu               sync.RWMutex
	rateLimits       map[string]*RateLimiter // Legacy: client_name -> rate limiter
	rateLimitService *RateLimitService       // New rate limiting service
}

// RateLimiter tracks request rates for clients
type RateLimiter struct {
	requests []time.Time
	limit    int
	window   time.Duration
	mu       sync.Mutex
}

// NewAuthService creates a new authentication service
func NewAuthService(config *models.AuthConfig) *AuthService {
	return &AuthService{
		config:           config,
		rateLimits:       make(map[string]*RateLimiter),
		rateLimitService: NewRateLimitService(),
	}
}

// SetRateLimitService sets the rate limiting service (for dependency injection)
func (a *AuthService) SetRateLimitService(rls *RateLimitService) {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.rateLimitService = rls
}

// UpdateConfig updates the authentication configuration
func (a *AuthService) UpdateConfig(config *models.AuthConfig) {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.config = config
}

// Authenticate validates client credentials and returns auth context
func (a *AuthService) Authenticate(clientName, token string) (*models.AuthContext, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// If auth is disabled globally, allow all requests
	if !a.config.Enabled {
		return &models.AuthContext{
			Authenticated: true,
			AuthMethod:    "disabled",
			RequestTime:   time.Now(),
		}, nil
	}

	// Validate required headers
	if clientName == "" || token == "" {
		return &models.AuthContext{
				Authenticated: false,
				RequestTime:   time.Now(),
			}, &models.AuthError{
				Code:    models.AuthErrorMissingHeaders,
				Message: "Missing authentication headers: x-auth-client and x-auth-token required",
			}
	}

	// Find client
	client := a.findClient(clientName)
	if client == nil {
		return &models.AuthContext{
				Authenticated: false,
				RequestTime:   time.Now(),
			}, &models.AuthError{
				Code:    models.AuthErrorInvalidClient,
				Message: fmt.Sprintf("Client '%s' not found", clientName),
			}
	}

	// Check if client is enabled
	if !client.Enabled {
		return &models.AuthContext{
				Authenticated: false,
				RequestTime:   time.Now(),
			}, &models.AuthError{
				Code:    models.AuthErrorDisabled,
				Message: fmt.Sprintf("Client '%s' is disabled", clientName),
			}
	}

	// Validate token
	if client.Token != token {
		return &models.AuthContext{
				Authenticated: false,
				RequestTime:   time.Now(),
			}, &models.AuthError{
				Code:    models.AuthErrorInvalidToken,
				Message: "Invalid authentication token",
			}
	}

	// Check legacy rate limit (for backward compatibility)
	if client.RateLimit > 0 {
		if !a.checkRateLimit(clientName, client.RateLimit) {
			return &models.AuthContext{
					Authenticated: false,
					RequestTime:   time.Now(),
				}, &models.AuthError{
					Code:    models.AuthErrorRateLimit,
					Message: fmt.Sprintf("Rate limit exceeded: %d requests per minute", client.RateLimit),
				}
		}
	}

	// Check new global rate limits
	if a.rateLimitService != nil && (client.GlobalRateLimit.RequestsPerSecond > 0 || client.GlobalRateLimit.RequestsPerMinute > 0) {
		if !a.rateLimitService.CheckClientRateLimit(clientName, client.GlobalRateLimit) {
			return &models.AuthContext{
					Authenticated: false,
					RequestTime:   time.Now(),
				}, &models.AuthError{
					Code:    models.AuthErrorRateLimit,
					Message: fmt.Sprintf("Client rate limit exceeded: %d RPS, %d RPM", client.GlobalRateLimit.RequestsPerSecond, client.GlobalRateLimit.RequestsPerMinute),
				}
		}
	}

	// Update last used time
	a.updateLastUsed(clientName)

	return &models.AuthContext{
		Client:        client,
		Authenticated: true,
		AuthMethod:    "header",
		RequestTime:   time.Now(),
	}, nil
}

// IsAuthorized checks if a client is authorized to call a specific function
func (a *AuthService) IsAuthorized(authCtx *models.AuthContext, functionName string) error {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// If auth is disabled, allow all
	if !a.config.Enabled {
		return nil
	}

	// Must be authenticated
	if !authCtx.Authenticated || authCtx.Client == nil {
		return &models.AuthError{
			Code:    models.AuthErrorAccessDenied,
			Message: "Authentication required",
		}
	}

	// Check if client has permission for this function
	for _, allowedFunc := range authCtx.Client.AllowedFunctions {
		if allowedFunc == functionName || allowedFunc == "*" {
			return nil
		}
	}

	return &models.AuthError{
		Code:    models.AuthErrorAccessDenied,
		Message: fmt.Sprintf("Client '%s' is not authorized to call function '%s'", authCtx.Client.Name, functionName),
		Details: fmt.Sprintf("Allowed functions: %s", strings.Join(authCtx.Client.AllowedFunctions, ", ")),
	}
}

// GetClientInfo returns information about a client (without sensitive data)
func (a *AuthService) GetClientInfo(clientName string) *models.Client {
	a.mu.RLock()
	defer a.mu.RUnlock()

	client := a.findClient(clientName)
	if client == nil {
		return nil
	}

	// Return copy without token
	return &models.Client{
		Name:             client.Name,
		AllowedFunctions: client.AllowedFunctions,
		Enabled:          client.Enabled,
		CreatedAt:        client.CreatedAt,
		LastUsed:         client.LastUsed,
		Description:      client.Description,
		RateLimit:        client.RateLimit,
	}
}

// ListClients returns all clients (without sensitive data)
func (a *AuthService) ListClients() []models.Client {
	a.mu.RLock()
	defer a.mu.RUnlock()

	clients := make([]models.Client, len(a.config.Clients))
	for i, client := range a.config.Clients {
		clients[i] = models.Client{
			Name:             client.Name,
			AllowedFunctions: client.AllowedFunctions,
			Enabled:          client.Enabled,
			CreatedAt:        client.CreatedAt,
			LastUsed:         client.LastUsed,
			Description:      client.Description,
			RateLimit:        client.RateLimit,
		}
	}
	return clients
}

// findClient finds a client by name (internal helper)
func (a *AuthService) findClient(name string) *models.Client {
	for i := range a.config.Clients {
		if a.config.Clients[i].Name == name {
			return &a.config.Clients[i]
		}
	}
	return nil
}

// checkRateLimit checks if client is within rate limit
func (a *AuthService) checkRateLimit(clientName string, limit int) bool {
	if limit <= 0 {
		return true
	}

	limiter, exists := a.rateLimits[clientName]
	if !exists {
		limiter = &RateLimiter{
			limit:  limit,
			window: time.Minute,
		}
		a.rateLimits[clientName] = limiter
	}

	return limiter.Allow()
}

// updateLastUsed updates the last used timestamp for a client
func (a *AuthService) updateLastUsed(clientName string) {
	for i := range a.config.Clients {
		if a.config.Clients[i].Name == clientName {
			a.config.Clients[i].LastUsed = time.Now()
			break
		}
	}
}

// Allow checks if request is allowed under rate limit
func (rl *RateLimiter) Allow() bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()

	// Remove old requests outside the window
	cutoff := now.Add(-rl.window)
	validRequests := make([]time.Time, 0, len(rl.requests))
	for _, reqTime := range rl.requests {
		if reqTime.After(cutoff) {
			validRequests = append(validRequests, reqTime)
		}
	}
	rl.requests = validRequests

	// Check if we're under the limit
	if len(rl.requests) >= rl.limit {
		return false
	}

	// Add current request
	rl.requests = append(rl.requests, now)
	return true
}

// CheckClientFunctionRateLimit checks if a client is within their function-specific rate limits
func (a *AuthService) CheckClientFunctionRateLimit(authCtx *models.AuthContext, functionName string) error {
	if a.rateLimitService == nil || authCtx == nil || authCtx.Client == nil {
		return nil
	}

	client := authCtx.Client

	// Check client-function specific rate limits
	for _, funcLimit := range client.FunctionRateLimits {
		if funcLimit.FunctionName == functionName {
			if !a.rateLimitService.CheckClientFunctionRateLimit(client.Name, functionName, funcLimit.RateLimit) {
				return &models.AuthError{
					Code:    models.AuthErrorRateLimit,
					Message: fmt.Sprintf("Client-function rate limit exceeded for %s:%s - %d RPS, %d RPM", client.Name, functionName, funcLimit.RateLimit.RequestsPerSecond, funcLimit.RateLimit.RequestsPerMinute),
				}
			}
			break
		}
	}

	return nil
}

// CheckFunctionRateLimit checks if a function is within its global rate limits
func (a *AuthService) CheckFunctionRateLimit(functionName string, functionConfig models.Function) error {
	if a.rateLimitService == nil {
		return nil
	}

	if !a.rateLimitService.CheckFunctionRateLimit(functionName, functionConfig.RateLimit) {
		return &models.AuthError{
			Code:    models.AuthErrorRateLimit,
			Message: fmt.Sprintf("Function rate limit exceeded for %s - %d RPS, %d RPM", functionName, functionConfig.RateLimit.RequestsPerSecond, functionConfig.RateLimit.RequestsPerMinute),
		}
	}

	return nil
}
