package services

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"

	lua "github.com/yuin/gopher-lua"

	"nova-proxy/internal/models"
	"nova-proxy/internal/utils"
)

// ResponseNormalizer handles response normalization from different partners
type ResponseNormalizer struct{}

// NewResponseNormalizer creates a new response normalizer
func NewResponseNormalizer() *ResponseNormalizer {
	return &ResponseNormalizer{}
}

// NormalizeResponse converts partner response to standard format
func (rn *ResponseNormalizer) NormalizeResponse(
	partnerResponse []byte,
	upstream *models.Upstream,
	partner *models.Partner,
	service string,
	startTime time.Time,
	requestID string,
) *models.StandardResponse {

	// Parse partner response
	var partnerData map[string]interface{}
	if err := json.Unmarshal(partnerResponse, &partnerData); err != nil {
		return rn.createErrorResponse(partner.Name, "PARSE_ERROR",
			"Failed to parse partner response", time.Since(startTime), requestID)
	}

	// If no response mapping configured, return raw response
	if partner.ResponseMapping == nil {
		return rn.createRawResponse(partnerData, partner.Name,
			time.Since(startTime), requestID)
	}

	mappingRules := partner.ResponseMapping

	// Extract and normalize data based on service type
	normalizedData := rn.normalizeServiceData(partnerData, mappingRules, service)

	// Create standard response
	return &models.StandardResponse{
		Data:      normalizedData,
		Engine:    partner.Name,
		Duration:  float64(time.Since(startTime).Nanoseconds()) / 1e6,
		RequestID: requestID,
		Timestamp: time.Now(),
	}
}

// normalizeServiceData normalizes data based on service type
func (rn *ResponseNormalizer) normalizeServiceData(
	data map[string]interface{},
	mapping *models.ResponseMappingRule,
	service string,
) map[string]interface{} {

	result := make(map[string]interface{})

	for standardField, partnerField := range mapping.FieldMappings {
		if value := rn.getNestedValue(data, partnerField, mapping.Mappers, service); value != nil {
			rn.setNested(result, standardField, value)
		}
	}

	return result
}

func (rn *ResponseNormalizer) getNestedValue(data map[string]interface{}, expr string, mappers map[string]map[string]string, service string) interface{} {
	parts := strings.Split(expr, "|")
	path := parts[0]
	val := rn.getByPath(data, path)

	if val == nil {
		return nil
	}
	for _, mod := range parts[1:] {
		if strings.HasPrefix(mod, "date:") {
			rule := strings.TrimPrefix(mod, "date:")
			val = rn.normalizeDate(fmt.Sprintf("%v", val), rule)
		} else if strings.HasPrefix(mod, "map:") {
			m := strings.TrimPrefix(mod, "map:")
			if mapper, ok := mappers[m]; ok {
				val = rn.mapValue(fmt.Sprintf("%v", val), mapper)
			}
		} else if strings.HasPrefix(mod, "func:") {
			m := strings.TrimPrefix(mod, "func:")
			val = rn.callFunction(m, val)
		} else if strings.HasPrefix(mod, "custom:") {
			m := strings.TrimPrefix(mod, "custom:")
			// call lua function here
			mparts := strings.Split(m, ":")

			params := strings.Split(mparts[1], ",")
			for i, p := range params {
				params[i] = fmt.Sprintf("%v", rn.getByPath(data, p))
			}
			val = rn.callCustomMap(mparts[0], params)
		}
	}
	return val
}

func (rn *ResponseNormalizer) callFunction(m string, val interface{}) interface{} {
	// if val is string, convert to []float64
	if reflect.TypeOf(val).Kind() == reflect.String {
		var nums []float64
		err := json.Unmarshal([]byte(val.(string)), &nums)
		if err != nil {
			return val
		}

		switch m {
		case "min":
			return utils.Min(nums)
		case "max":
			return utils.Max(nums)
		}
	}

	return val
}

func (rn *ResponseNormalizer) callCustomMap(funcPath string, params []string) interface{} {
	L := lua.NewState()
	defer L.Close()

	fparts := strings.Split(funcPath, ".")

	// Check if file is exist
	filepath := fparts[0] + ".lua"
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		fmt.Printf("Lua file not found: %s\n", filepath)
		return nil
	}

	// Load the Lua script that defines a concatenation function
	if err := L.DoFile(filepath); err != nil {
		fmt.Printf("Error loading lua file: %v\n", err)
		return nil
	}

	// convert array to lua Strings
	luaParams := make([]lua.LValue, len(params))
	for i, p := range params {
		luaParams[i] = lua.LString(p)
	}

	if err := L.CallByParam(lua.P{
		Fn:      L.GetGlobal(fparts[1]),
		NRet:    1,
		Protect: true,
	}, luaParams...); err != nil {
		fmt.Printf("Error calling lua function: %v\n", err)
		return nil
	}

	// Retrieve the result from the stack
	ret := L.Get(-1)
	L.Pop(1)

	// Print the concatenated result from Go
	if lstr, ok := ret.(lua.LString); ok {
		return string(lstr)
	} else {
		fmt.Printf("Unexpected return type: %v\n", ret)
		return nil
	}
}

func (rn *ResponseNormalizer) getByPath(m map[string]interface{}, path string) interface{} {
	if path == "" || path == "." || path == "@" {
		return m
	}
	parts := strings.Split(path, ".")
	var cur interface{} = m
	for _, p := range parts {
		if mm, ok := cur.(map[string]interface{}); ok {
			cur = mm[p]
		} else {
			return nil
		}
	}
	return cur
}

// setNested sets a value into nested map like "person.name"
func (rn *ResponseNormalizer) setNested(m map[string]interface{}, path string, val interface{}) {
	parts := strings.Split(path, ".")
	cur := m
	for i, p := range parts {
		if i == len(parts)-1 {
			cur[p] = val
			return
		}
		if _, ok := cur[p]; !ok {
			cur[p] = map[string]interface{}{}
		}
		next, _ := cur[p].(map[string]interface{})
		cur = next
	}
}

func (rn *ResponseNormalizer) mapValue(raw string, mapper map[string]string) string {
	if raw == "<nil>" || raw == "" {
		return ""
	}

	// raw to lower case before mapping
	raw = strings.ToLower(raw)
	if mapped, ok := mapper[raw]; ok {
		return mapped
	}
	return raw
}

// convert format dd/MM/yyyy->yyyy-MM-dd using Go layouts
func (rn *ResponseNormalizer) convertFormat(fmtStr string) string {
	r := strings.NewReplacer(
		"dd", "02",
		"MM", "01",
		"yyyy", "2006",
		"yy", "06",
		"HH", "15",
		"mm", "04",
		"ss", "05",
	)
	return r.Replace(fmtStr)
}

func (rn *ResponseNormalizer) normalizeDate(value string, rule string) string {
	if value == "" || value == "<nil>" {
		return ""
	}
	parts := strings.Split(rule, "->")
	if len(parts) != 2 {
		return value
	}
	from := rn.convertFormat(strings.TrimSpace(parts[0]))
	to := rn.convertFormat(strings.TrimSpace(parts[1]))
	t, err := time.Parse(from, value)
	if err != nil {
		// try a lenient parse with common formats
		tryLayouts := []string{"02/01/2006", "2006-01-02", time.RFC3339}
		for _, lay := range tryLayouts {
			t2, err2 := time.Parse(lay, value)
			if err2 == nil {
				return t2.Format(to)
			}
		}
		return value
	}
	return t.Format(to)
}

func (rn *ResponseNormalizer) valuesEqual(a, b interface{}) bool {
	return fmt.Sprint(a) == fmt.Sprint(b)
}

func (rn *ResponseNormalizer) toFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", value)
	}
}

func (rn *ResponseNormalizer) createErrorResponse(engine, code, message string, duration time.Duration, requestID string) *models.ErrorResponse {
	return &models.ErrorResponse{
		Code:    code,
		Message: &message,
		StandardResponse: models.StandardResponse{
			Timestamp: time.Now(),
			Engine:    engine,
			Duration:  float64(duration.Nanoseconds()) / 1e6,
			RequestID: requestID,
		},
	}
}

func (rn *ResponseNormalizer) createErrorFromPartnerResponse(data map[string]interface{}, mapping *models.ResponseMappingRule, service string, engine string, duration time.Duration, requestID string) *models.ErrorResponse {
	errorCode := "PARTNER_ERROR"
	errorMessage := "Request failed"

	if mapping.ErrorCodeField != "" {
		if code := rn.getNestedValue(data, mapping.ErrorCodeField, mapping.Mappers, service); code != nil {
			errorCode = fmt.Sprint(code)
		}
	}

	if mapping.MessageField != "" {
		if msg := rn.getNestedValue(data, mapping.MessageField, mapping.Mappers, service); msg != nil {
			errorMessage = fmt.Sprint(msg)
		}
	}

	return rn.createErrorResponse(engine, errorCode, errorMessage, duration, requestID)
}

func (rn *ResponseNormalizer) createRawResponse(data map[string]interface{}, engine string, duration time.Duration, requestID string) *models.StandardResponse {
	return &models.StandardResponse{
		Data:      data,
		Engine:    engine,
		Duration:  float64(duration.Nanoseconds()) / 1e6,
		RequestID: requestID,
		Timestamp: time.Now(),
	}
}
