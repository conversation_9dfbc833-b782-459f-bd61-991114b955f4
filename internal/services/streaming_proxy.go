package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"net/http"

	"nova-proxy/internal/models"
	"nova-proxy/internal/utils"

	"github.com/gin-gonic/gin"
)

// StreamingProxyService provides optimized file streaming for proxy requests
type StreamingProxyService struct {
	httpClient *http.Client
}

// NewStreamingProxyService creates a new streaming proxy service
func NewStreamingProxyService(httpClient *http.Client) *StreamingProxyService {
	return &StreamingProxyService{
		httpClient: httpClient,
	}
}

// StreamingMultipartRequest represents a streaming multipart request
type StreamingMultipartRequest struct {
	Files  map[string]*multipart.FileHeader
	Fields map[string]string
}

// ParseStreamingRequest parses multipart form data without buffering file content
func (s *StreamingProxyService) ParseStreamingRequest(c *gin.Context, service string) (*StreamingMultipartRequest, error) {
	// Parse multipart form with size limit
	maxMemory := int64(32 << 20) // 32MB max memory for form parsing
	if err := c.Request.ParseMultipartForm(maxMemory); err != nil {
		return nil, fmt.Errorf("failed to parse multipart form: %v", err)
	}

	req := &StreamingMultipartRequest{
		Files:  make(map[string]*multipart.FileHeader),
		Fields: make(map[string]string),
	}

	// Extract files without reading content
	if c.Request.MultipartForm != nil {
		for fieldName, fileHeaders := range c.Request.MultipartForm.File {
			if len(fileHeaders) > 0 {
				req.Files[fieldName] = fileHeaders[0]
			}
		}

		// Extract form fields
		for fieldName, values := range c.Request.MultipartForm.Value {
			if len(values) > 0 {
				req.Fields[fieldName] = values[0]
			}
		}
	}

	return req, nil
}

// StreamToUpstream streams the request directly to upstream without intermediate buffering
func (s *StreamingProxyService) StreamToUpstream(
	c *gin.Context,
	upstream *models.Upstream,
	partner *models.Partner,
	streamingReq *StreamingMultipartRequest,
	fieldMapping map[string]string,
) (*http.Response, error) {
	// Create pipe for streaming
	pr, pw := io.Pipe()

	// Create multipart writer
	mw := multipart.NewWriter(pw)

	// Start goroutine to write multipart data
	go func() {
		defer pw.Close()
		defer mw.Close()

		// Write files with field mapping
		for originalField, fileHeader := range streamingReq.Files {
			// Apply field mapping
			targetField := originalField
			if mapped, exists := fieldMapping[originalField]; exists {
				targetField = mapped
			}

			// Create form file
			fw, err := mw.CreateFormFile(targetField, fileHeader.Filename)
			if err != nil {
				pw.CloseWithError(err)
				return
			}

			// Open and stream file content
			file, err := fileHeader.Open()
			if err != nil {
				pw.CloseWithError(err)
				return
			}
			defer file.Close()

			// Stream file content directly with pooled buffer
			if fileHeader.Size > 1024*1024 { // > 1MB
				buffer := utils.ByteSlicePool32K.Get()
				defer utils.ByteSlicePool32K.Put(buffer)
				_, err = io.CopyBuffer(fw, file, buffer)
			} else {
				_, err = io.Copy(fw, file)
			}

			if err != nil {
				pw.CloseWithError(err)
				return
			}
		}

		// Write form fields with field mapping
		for originalField, value := range streamingReq.Fields {
			// Apply field mapping
			targetField := originalField
			if mapped, exists := fieldMapping[originalField]; exists {
				targetField = mapped
			}

			if err := mw.WriteField(targetField, value); err != nil {
				pw.CloseWithError(err)
				return
			}
		}

		// Write hardcoded fields
		for key, value := range upstream.HardcodedFields {
			if err := mw.WriteField(key, value); err != nil {
				pw.CloseWithError(err)
				return
			}
		}
	}()

	// Create HTTP request with streaming body
	req, err := http.NewRequest(c.Request.Method, upstream.URL, pr)
	if err != nil {
		return nil, err
	}

	// Set content type
	req.Header.Set("Content-Type", mw.FormDataContentType())

	// Copy relevant headers from original request
	s.copyHeaders(c.Request, req, upstream)

	// Execute request
	return s.httpClient.Do(req)
}

// copyHeaders copies relevant headers from source to destination request
func (s *StreamingProxyService) copyHeaders(src *http.Request, dst *http.Request, upstream *models.Upstream) {
	// Copy standard headers
	headersToProxy := []string{
		"User-Agent",
		"Accept",
		"Accept-Encoding",
		"Accept-Language",
		"Cache-Control",
		"Connection",
	}

	for _, header := range headersToProxy {
		if value := src.Header.Get(header); value != "" {
			dst.Header.Set(header, value)
		}
	}

	// Add hardcoded headers from upstream config
	for key, value := range upstream.HardcodedHeaders {
		dst.Header.Set(key, value)
	}
}

// ValidateStreamingRequest validates the streaming request against service requirements
func (s *StreamingProxyService) ValidateStreamingRequest(streamingReq *StreamingMultipartRequest, service string) error {
	var requiredFields []string

	switch service {
	case "ocr":
		requiredFields = []string{"img1", "img2"}
	case "liveness":
		requiredFields = []string{"img1", "img2", "img3"}
	case "facematch":
		requiredFields = []string{"img1", "img2"}
	default:
		return fmt.Errorf("unknown service: %s", service)
	}

	// Check required files
	for _, field := range requiredFields {
		if _, exists := streamingReq.Files[field]; !exists {
			return fmt.Errorf("missing required file field: %s", field)
		}
	}

	return nil
}

// GetRequestSizeEstimate estimates the total request size for monitoring
func (s *StreamingProxyService) GetRequestSizeEstimate(streamingReq *StreamingMultipartRequest) int64 {
	var totalSize int64

	// Sum file sizes
	for _, fileHeader := range streamingReq.Files {
		totalSize += fileHeader.Size
	}

	// Estimate form field sizes
	for _, value := range streamingReq.Fields {
		totalSize += int64(len(value))
	}

	// Add multipart overhead estimate (boundaries, headers, etc.)
	overhead := int64(len(streamingReq.Files)*200 + len(streamingReq.Fields)*100)

	return totalSize + overhead
}

// StreamResponse streams the upstream response directly to the client
func (s *StreamingProxyService) StreamResponse(c *gin.Context, resp *http.Response) error {
	// Copy response headers
	for key, values := range resp.Header {
		for _, value := range values {
			c.Header(key, value)
		}
	}

	// Set status code
	c.Status(resp.StatusCode)

	// Stream response body directly
	_, err := io.Copy(c.Writer, resp.Body)
	return err
}
