package services

import (
	"fmt"
	"sync"
	"time"

	"nova-proxy/internal/models"
)

// RateLimiterType represents the type of rate limiter
type RateLimiterType string

const (
	RateLimiterTypeRPS RateLimiterType = "rps" // Requests per second
	RateLimiterTypeRPM RateLimiterType = "rpm" // Requests per minute
)

// MultiRateLimiter handles both RPS and RPM rate limiting
type MultiRateLimiter struct {
	rpsLimiter *TokenBucketLimiter
	rpmLimiter *SlidingWindowLimiter
	mu         sync.RWMutex
}

// TokenBucketLimiter implements token bucket algorithm for RPS limiting
type TokenBucketLimiter struct {
	capacity   int
	tokens     int
	refillRate int // tokens per second
	lastRefill time.Time
	mu         sync.Mutex
}

// SlidingWindowLimiter implements sliding window algorithm for RPM limiting
type SlidingWindowLimiter struct {
	requests []time.Time
	limit    int
	window   time.Duration
	mu       sync.Mutex
}

// RateLimitService manages all rate limiting across the system
type RateLimitService struct {
	// Client-level rate limiters
	clientLimiters map[string]*MultiRateLimiter // client_name -> limiter

	// Client-function rate limiters
	clientFunctionLimiters map[string]*MultiRateLimiter // "client_name:function_name" -> limiter

	// Function-level rate limiters
	functionLimiters map[string]*MultiRateLimiter // function_name -> limiter

	// Partner-level rate limiters
	partnerLimiters map[string]*MultiRateLimiter // partner_name -> limiter

	// Partner-function rate limiters
	partnerFunctionLimiters map[string]*MultiRateLimiter // "partner_name:function_name" -> limiter

	mu sync.RWMutex
}

// NewRateLimitService creates a new rate limiting service
func NewRateLimitService() *RateLimitService {
	return &RateLimitService{
		clientLimiters:          make(map[string]*MultiRateLimiter),
		clientFunctionLimiters:  make(map[string]*MultiRateLimiter),
		functionLimiters:        make(map[string]*MultiRateLimiter),
		partnerLimiters:         make(map[string]*MultiRateLimiter),
		partnerFunctionLimiters: make(map[string]*MultiRateLimiter),
	}
}

// NewMultiRateLimiter creates a new multi-rate limiter
func NewMultiRateLimiter(rpsLimit, rpmLimit int) *MultiRateLimiter {
	limiter := &MultiRateLimiter{}

	if rpsLimit > 0 {
		limiter.rpsLimiter = &TokenBucketLimiter{
			capacity:   rpsLimit,
			tokens:     rpsLimit,
			refillRate: rpsLimit,
			lastRefill: time.Now(),
		}
	}

	if rpmLimit > 0 {
		limiter.rpmLimiter = &SlidingWindowLimiter{
			requests: make([]time.Time, 0),
			limit:    rpmLimit,
			window:   time.Minute,
		}
	}

	return limiter
}

// Allow checks if a request is allowed under both RPS and RPM limits
func (mrl *MultiRateLimiter) Allow() bool {
	mrl.mu.RLock()
	defer mrl.mu.RUnlock()

	// Check RPS limit first (more restrictive)
	if mrl.rpsLimiter != nil && !mrl.rpsLimiter.Allow() {
		return false
	}

	// Check RPM limit
	if mrl.rpmLimiter != nil && !mrl.rpmLimiter.Allow() {
		return false
	}

	return true
}

// Allow checks if request is allowed under RPS limit (token bucket)
func (tbl *TokenBucketLimiter) Allow() bool {
	tbl.mu.Lock()
	defer tbl.mu.Unlock()

	now := time.Now()
	elapsed := now.Sub(tbl.lastRefill).Seconds()

	// Refill tokens based on elapsed time
	tokensToAdd := int(elapsed * float64(tbl.refillRate))
	if tokensToAdd > 0 {
		tbl.tokens += tokensToAdd
		if tbl.tokens > tbl.capacity {
			tbl.tokens = tbl.capacity
		}
		tbl.lastRefill = now
	}

	// Check if we have tokens available
	if tbl.tokens <= 0 {
		return false
	}

	// Consume a token
	tbl.tokens--
	return true
}

// Allow checks if request is allowed under RPM limit (sliding window)
func (swl *SlidingWindowLimiter) Allow() bool {
	swl.mu.Lock()
	defer swl.mu.Unlock()

	now := time.Now()
	cutoff := now.Add(-swl.window)

	// Remove old requests outside the window
	validRequests := make([]time.Time, 0, len(swl.requests))
	for _, reqTime := range swl.requests {
		if reqTime.After(cutoff) {
			validRequests = append(validRequests, reqTime)
		}
	}
	swl.requests = validRequests

	// Check if we're under the limit
	if len(swl.requests) >= swl.limit {
		return false
	}

	// Add current request
	swl.requests = append(swl.requests, now)
	return true
}

// CheckClientRateLimit checks if a client is within their global rate limits
func (rls *RateLimitService) CheckClientRateLimit(clientName string, rateLimit models.RateLimitConfig) bool {
	if rateLimit.RequestsPerSecond == 0 && rateLimit.RequestsPerMinute == 0 {
		return true // No limits configured
	}

	rls.mu.Lock()
	limiter, exists := rls.clientLimiters[clientName]
	if !exists {
		limiter = NewMultiRateLimiter(rateLimit.RequestsPerSecond, rateLimit.RequestsPerMinute)
		rls.clientLimiters[clientName] = limiter
	}
	rls.mu.Unlock()

	return limiter.Allow()
}

// CheckClientFunctionRateLimit checks if a client is within their function-specific rate limits
func (rls *RateLimitService) CheckClientFunctionRateLimit(clientName, functionName string, rateLimit models.RateLimitConfig) bool {
	if rateLimit.RequestsPerSecond == 0 && rateLimit.RequestsPerMinute == 0 {
		return true // No limits configured
	}

	key := fmt.Sprintf("%s:%s", clientName, functionName)

	rls.mu.Lock()
	limiter, exists := rls.clientFunctionLimiters[key]
	if !exists {
		limiter = NewMultiRateLimiter(rateLimit.RequestsPerSecond, rateLimit.RequestsPerMinute)
		rls.clientFunctionLimiters[key] = limiter
	}
	rls.mu.Unlock()

	return limiter.Allow()
}

// CheckFunctionRateLimit checks if a function is within its global rate limits
func (rls *RateLimitService) CheckFunctionRateLimit(functionName string, rateLimit models.RateLimitConfig) bool {
	if rateLimit.RequestsPerSecond == 0 && rateLimit.RequestsPerMinute == 0 {
		return true // No limits configured
	}

	rls.mu.Lock()
	limiter, exists := rls.functionLimiters[functionName]
	if !exists {
		limiter = NewMultiRateLimiter(rateLimit.RequestsPerSecond, rateLimit.RequestsPerMinute)
		rls.functionLimiters[functionName] = limiter
	}
	rls.mu.Unlock()

	return limiter.Allow()
}

// CheckPartnerRateLimit checks if a partner is within their global rate limits
func (rls *RateLimitService) CheckPartnerRateLimit(partnerName string, rateLimit models.RateLimitConfig) bool {
	if rateLimit.RequestsPerSecond == 0 && rateLimit.RequestsPerMinute == 0 {
		return true // No limits configured
	}

	rls.mu.Lock()
	limiter, exists := rls.partnerLimiters[partnerName]
	if !exists {
		limiter = NewMultiRateLimiter(rateLimit.RequestsPerSecond, rateLimit.RequestsPerMinute)
		rls.partnerLimiters[partnerName] = limiter
	}
	rls.mu.Unlock()

	return limiter.Allow()
}

// CheckPartnerFunctionRateLimit checks if a partner is within their function-specific rate limits
func (rls *RateLimitService) CheckPartnerFunctionRateLimit(partnerName, functionName string, rateLimit models.RateLimitConfig) bool {
	if rateLimit.RequestsPerSecond == 0 && rateLimit.RequestsPerMinute == 0 {
		return true // No limits configured
	}

	key := fmt.Sprintf("%s:%s", partnerName, functionName)

	rls.mu.Lock()
	limiter, exists := rls.partnerFunctionLimiters[key]
	if !exists {
		limiter = NewMultiRateLimiter(rateLimit.RequestsPerSecond, rateLimit.RequestsPerMinute)
		rls.partnerFunctionLimiters[key] = limiter
	}
	rls.mu.Unlock()

	return limiter.Allow()
}

// IsPartnerAvailable checks if a partner can handle requests (not rate limited)
func (rls *RateLimitService) IsPartnerAvailable(partnerName, functionName string, partner *models.Partner) bool {
	// Check partner global rate limit
	if !rls.CheckPartnerRateLimit(partnerName, partner.GlobalRateLimit) {
		return false
	}

	// Check partner-function specific rate limit
	for _, funcLimit := range partner.FunctionRateLimits {
		if funcLimit.FunctionName == functionName {
			if !rls.CheckPartnerFunctionRateLimit(partnerName, functionName, funcLimit.RateLimit) {
				return false
			}
			break
		}
	}

	return true
}

// GetAvailablePartners returns a list of partners that are not rate limited for a specific function
func (rls *RateLimitService) GetAvailablePartners(functionName string, partners []models.Partner) []models.Partner {
	available := make([]models.Partner, 0, len(partners))

	for _, partner := range partners {
		if rls.IsPartnerAvailable(partner.Name, functionName, &partner) {
			available = append(available, partner)
		}
	}

	return available
}

// RateLimitStats represents rate limiting statistics
type RateLimitStats struct {
	ClientStats          map[string]LimiterStats `json:"client_stats"`
	ClientFunctionStats  map[string]LimiterStats `json:"client_function_stats"`
	FunctionStats        map[string]LimiterStats `json:"function_stats"`
	PartnerStats         map[string]LimiterStats `json:"partner_stats"`
	PartnerFunctionStats map[string]LimiterStats `json:"partner_function_stats"`
}

// LimiterStats represents statistics for a single rate limiter
type LimiterStats struct {
	RPSTokensRemaining  int `json:"rps_tokens_remaining"`
	RPMRequestsInWindow int `json:"rpm_requests_in_window"`
	RPSLimit            int `json:"rps_limit"`
	RPMLimit            int `json:"rpm_limit"`
}

// GetStats returns current rate limiting statistics
func (rls *RateLimitService) GetStats() RateLimitStats {
	rls.mu.RLock()
	defer rls.mu.RUnlock()

	stats := RateLimitStats{
		ClientStats:          make(map[string]LimiterStats),
		ClientFunctionStats:  make(map[string]LimiterStats),
		FunctionStats:        make(map[string]LimiterStats),
		PartnerStats:         make(map[string]LimiterStats),
		PartnerFunctionStats: make(map[string]LimiterStats),
	}

	// Collect client stats
	for key, limiter := range rls.clientLimiters {
		stats.ClientStats[key] = getLimiterStats(limiter)
	}

	// Collect client-function stats
	for key, limiter := range rls.clientFunctionLimiters {
		stats.ClientFunctionStats[key] = getLimiterStats(limiter)
	}

	// Collect function stats
	for key, limiter := range rls.functionLimiters {
		stats.FunctionStats[key] = getLimiterStats(limiter)
	}

	// Collect partner stats
	for key, limiter := range rls.partnerLimiters {
		stats.PartnerStats[key] = getLimiterStats(limiter)
	}

	// Collect partner-function stats
	for key, limiter := range rls.partnerFunctionLimiters {
		stats.PartnerFunctionStats[key] = getLimiterStats(limiter)
	}

	return stats
}

// getLimiterStats extracts statistics from a multi-rate limiter
func getLimiterStats(limiter *MultiRateLimiter) LimiterStats {
	limiter.mu.RLock()
	defer limiter.mu.RUnlock()

	stats := LimiterStats{}

	if limiter.rpsLimiter != nil {
		limiter.rpsLimiter.mu.Lock()
		stats.RPSTokensRemaining = limiter.rpsLimiter.tokens
		stats.RPSLimit = limiter.rpsLimiter.capacity
		limiter.rpsLimiter.mu.Unlock()
	}

	if limiter.rpmLimiter != nil {
		limiter.rpmLimiter.mu.Lock()
		now := time.Now()
		cutoff := now.Add(-limiter.rpmLimiter.window)

		// Count valid requests in window
		validCount := 0
		for _, reqTime := range limiter.rpmLimiter.requests {
			if reqTime.After(cutoff) {
				validCount++
			}
		}

		stats.RPMRequestsInWindow = validCount
		stats.RPMLimit = limiter.rpmLimiter.limit
		limiter.rpmLimiter.mu.Unlock()
	}

	return stats
}

// ResetLimiter resets a specific rate limiter (useful for testing or admin operations)
func (rls *RateLimitService) ResetLimiter(limiterType, key string) {
	rls.mu.Lock()
	defer rls.mu.Unlock()

	switch limiterType {
	case "client":
		delete(rls.clientLimiters, key)
	case "client-function":
		delete(rls.clientFunctionLimiters, key)
	case "function":
		delete(rls.functionLimiters, key)
	case "partner":
		delete(rls.partnerLimiters, key)
	case "partner-function":
		delete(rls.partnerFunctionLimiters, key)
	}
}
