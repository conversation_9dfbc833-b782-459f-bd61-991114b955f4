package services

import (
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// MetricsService handles Prometheus metrics collection
type MetricsService struct {
	// Request metrics
	requestsTotal    *prometheus.CounterVec
	requestDuration  *prometheus.HistogramVec
	requestsInFlight prometheus.Gauge
	requestSize      *prometheus.HistogramVec
	responseSize     *prometheus.HistogramVec

	// Authentication metrics
	authRequestsTotal *prometheus.CounterVec
	authFailuresTotal *prometheus.CounterVec

	// Rate limiting metrics
	rateLimitHitsTotal   *prometheus.CounterVec
	rateLimitChecksTotal *prometheus.CounterVec

	// Partner/upstream metrics
	upstreamRequestsTotal   *prometheus.CounterVec
	upstreamRequestDuration *prometheus.HistogramVec
	upstreamResponsesTotal  *prometheus.CounterVec

	// Service-specific metrics
	serviceRequestsTotal   *prometheus.CounterVec
	serviceRequestDuration *prometheus.HistogramVec
	serviceErrorsTotal     *prometheus.CounterVec

	// Business metrics
	businessCodeRequestsTotal *prometheus.CounterVec
	clientRequestsTotal       *prometheus.CounterVec

	// Latency metrics
	proxyLatency    *prometheus.HistogramVec
	upstreamLatency *prometheus.HistogramVec
}

// NewMetricsService creates a new metrics service with all Prometheus metrics
func NewMetricsService() *MetricsService {
	return NewMetricsServiceWithRegistry(prometheus.DefaultRegisterer)
}

// NewMetricsServiceWithRegistry creates a new metrics service with a custom registry
func NewMetricsServiceWithRegistry(registerer prometheus.Registerer) *MetricsService {
	factory := promauto.With(registerer)

	return &MetricsService{
		// Request metrics
		requestsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_requests_total",
				Help: "Total number of HTTP requests processed",
			},
			[]string{"method", "endpoint", "status_code"},
		),

		requestDuration: factory.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "endpoint", "status_code"},
		),

		requestsInFlight: factory.NewGauge(
			prometheus.GaugeOpts{
				Name: "nova_proxy_requests_in_flight",
				Help: "Number of HTTP requests currently being processed",
			},
		),

		requestSize: factory.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_request_size_bytes",
				Help:    "HTTP request size in bytes",
				Buckets: []float64{1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072, 262144, 524288, 1048576},
			},
			[]string{"method", "endpoint"},
		),

		responseSize: factory.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_response_size_bytes",
				Help:    "HTTP response size in bytes",
				Buckets: []float64{1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072, 262144, 524288, 1048576},
			},
			[]string{"method", "endpoint", "status_code"},
		),

		// Authentication metrics
		authRequestsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_auth_requests_total",
				Help: "Total number of authentication requests",
			},
			[]string{"client", "result"},
		),

		authFailuresTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_auth_failures_total",
				Help: "Total number of authentication failures",
			},
			[]string{"client", "reason"},
		),

		// Rate limiting metrics
		rateLimitHitsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_rate_limit_hits_total",
				Help: "Total number of rate limit hits",
			},
			[]string{"client", "function", "limit_type"},
		),

		rateLimitChecksTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_rate_limit_checks_total",
				Help: "Total number of rate limit checks",
			},
			[]string{"client", "function", "result"},
		),

		// Partner/upstream metrics
		upstreamRequestsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_upstream_requests_total",
				Help: "Total number of requests sent to upstream partners",
			},
			[]string{"partner", "service", "status_code"},
		),

		upstreamRequestDuration: factory.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_upstream_request_duration_seconds",
				Help:    "Upstream request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"partner", "service"},
		),

		upstreamResponsesTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_upstream_responses_total",
				Help: "Total number of responses received from upstream partners",
			},
			[]string{"partner", "service", "status_code", "success"},
		),

		// Service-specific metrics
		serviceRequestsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_service_requests_total",
				Help: "Total number of requests per service",
			},
			[]string{"service", "client", "partner"},
		),

		serviceRequestDuration: factory.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_service_request_duration_seconds",
				Help:    "Service request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"service", "partner"},
		),

		serviceErrorsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_service_errors_total",
				Help: "Total number of service errors",
			},
			[]string{"service", "partner", "error_type"},
		),

		// Business metrics
		businessCodeRequestsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_business_code_requests_total",
				Help: "Total number of requests per business code",
			},
			[]string{"business_code", "service", "partner"},
		),

		clientRequestsTotal: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_client_requests_total",
				Help: "Total number of requests per client",
			},
			[]string{"client", "service", "status_code"},
		),

		// Latency metrics
		proxyLatency: factory.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_processing_latency_seconds",
				Help:    "Proxy processing latency in seconds (excluding upstream)",
				Buckets: []float64{0.001, 0.002, 0.005, 0.01, 0.015, 0.02, 0.025, 0.05, 0.1, 0.15, 0.2, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0},
			},
			[]string{"service", "partner", "handler_type"},
		),

		upstreamLatency: factory.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_upstream_latency_seconds",
				Help:    "Upstream request latency in seconds",
				Buckets: []float64{0.001, 0.002, 0.005, 0.01, 0.015, 0.02, 0.025, 0.05, 0.1, 0.15, 0.2, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0},
			},
			[]string{"service", "partner", "upstream_url"},
		),
	}
}

// NewMetricsServiceOld creates a new metrics service with all Prometheus metrics (old implementation)
func NewMetricsServiceOld() *MetricsService {
	return &MetricsService{
		// Request metrics
		requestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_requests_total",
				Help: "Total number of HTTP requests processed",
			},
			[]string{"method", "endpoint", "status_code"},
		),

		requestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "endpoint", "status_code"},
		),

		requestsInFlight: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "nova_proxy_requests_in_flight",
				Help: "Number of HTTP requests currently being processed",
			},
		),

		requestSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_request_size_bytes",
				Help:    "HTTP request size in bytes",
				Buckets: prometheus.ExponentialBuckets(1024, 2, 10), // 1KB to 512MB
			},
			[]string{"method", "endpoint"},
		),

		responseSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_response_size_bytes",
				Help:    "HTTP response size in bytes",
				Buckets: prometheus.ExponentialBuckets(1024, 2, 10), // 1KB to 512MB
			},
			[]string{"method", "endpoint", "status_code"},
		),

		// Authentication metrics
		authRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_auth_requests_total",
				Help: "Total number of authentication requests",
			},
			[]string{"client", "result"},
		),

		authFailuresTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_auth_failures_total",
				Help: "Total number of authentication failures",
			},
			[]string{"client", "reason"},
		),

		// Rate limiting metrics
		rateLimitHitsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_rate_limit_hits_total",
				Help: "Total number of rate limit hits",
			},
			[]string{"client", "function", "limit_type"},
		),

		rateLimitChecksTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_rate_limit_checks_total",
				Help: "Total number of rate limit checks performed",
			},
			[]string{"client", "function", "result"},
		),

		// Partner/upstream metrics
		upstreamRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_upstream_requests_total",
				Help: "Total number of requests sent to upstream partners",
			},
			[]string{"partner", "service", "status_code"},
		),

		upstreamRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_upstream_request_duration_seconds",
				Help:    "Duration of requests to upstream partners in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"partner", "service"},
		),

		upstreamResponsesTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_upstream_responses_total",
				Help: "Total number of responses received from upstream partners",
			},
			[]string{"partner", "service", "status_code", "success"},
		),

		// Service-specific metrics
		serviceRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_service_requests_total",
				Help: "Total number of requests per service",
			},
			[]string{"service", "client", "partner"},
		),

		serviceRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_service_request_duration_seconds",
				Help:    "Service request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"service", "partner"},
		),

		serviceErrorsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_service_errors_total",
				Help: "Total number of service errors",
			},
			[]string{"service", "partner", "error_type"},
		),

		// Business metrics
		businessCodeRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_business_code_requests_total",
				Help: "Total number of requests per business code",
			},
			[]string{"business_code", "service", "partner"},
		),

		clientRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "nova_proxy_client_requests_total",
				Help: "Total number of requests per client",
			},
			[]string{"client", "service", "status_code"},
		),

		// Latency metrics
		proxyLatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_processing_latency_seconds",
				Help:    "Proxy processing latency in seconds (excluding upstream)",
				Buckets: []float64{0.001, 0.002, 0.005, 0.01, 0.015, 0.02, 0.025, 0.05, 0.1, 0.15, 0.2, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0},
			},
			[]string{"service", "partner", "handler_type"},
		),

		upstreamLatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "nova_proxy_upstream_latency_seconds",
				Help:    "Upstream request latency in seconds",
				Buckets: []float64{0.001, 0.002, 0.005, 0.01, 0.015, 0.02, 0.025, 0.05, 0.1, 0.15, 0.2, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0},
			},
			[]string{"service", "partner", "upstream_url"},
		),
	}
}

// RecordRequest records basic request metrics
func (m *MetricsService) RecordRequest(method, endpoint string, statusCode int, duration time.Duration, requestSize, responseSize int64) {
	statusStr := strconv.Itoa(statusCode)

	m.requestsTotal.WithLabelValues(method, endpoint, statusStr).Inc()
	m.requestDuration.WithLabelValues(method, endpoint, statusStr).Observe(duration.Seconds())

	if requestSize > 0 {
		m.requestSize.WithLabelValues(method, endpoint).Observe(float64(requestSize))
	}
	if responseSize > 0 {
		m.responseSize.WithLabelValues(method, endpoint, statusStr).Observe(float64(responseSize))
	}
}

// IncRequestsInFlight increments the in-flight requests counter
func (m *MetricsService) IncRequestsInFlight() {
	m.requestsInFlight.Inc()
}

// DecRequestsInFlight decrements the in-flight requests counter
func (m *MetricsService) DecRequestsInFlight() {
	m.requestsInFlight.Dec()
}

// RecordAuthRequest records authentication metrics
func (m *MetricsService) RecordAuthRequest(client, result string) {
	m.authRequestsTotal.WithLabelValues(client, result).Inc()
}

// RecordAuthFailure records authentication failure metrics
func (m *MetricsService) RecordAuthFailure(client, reason string) {
	m.authFailuresTotal.WithLabelValues(client, reason).Inc()
}

// RecordRateLimitHit records rate limit hit metrics
func (m *MetricsService) RecordRateLimitHit(client, function, limitType string) {
	m.rateLimitHitsTotal.WithLabelValues(client, function, limitType).Inc()
}

// RecordRateLimitCheck records rate limit check metrics
func (m *MetricsService) RecordRateLimitCheck(client, function, result string) {
	m.rateLimitChecksTotal.WithLabelValues(client, function, result).Inc()
}

// RecordUpstreamRequest records upstream request metrics
func (m *MetricsService) RecordUpstreamRequest(partner, service string, statusCode int, duration time.Duration, success bool) {
	statusStr := strconv.Itoa(statusCode)
	successStr := strconv.FormatBool(success)

	m.upstreamRequestsTotal.WithLabelValues(partner, service, statusStr).Inc()
	m.upstreamRequestDuration.WithLabelValues(partner, service).Observe(duration.Seconds())
	m.upstreamResponsesTotal.WithLabelValues(partner, service, statusStr, successStr).Inc()
}

// RecordServiceRequest records service-specific metrics
func (m *MetricsService) RecordServiceRequest(service, client, partner string, duration time.Duration) {
	m.serviceRequestsTotal.WithLabelValues(service, client, partner).Inc()
	m.serviceRequestDuration.WithLabelValues(service, partner).Observe(duration.Seconds())
}

// RecordServiceError records service error metrics
func (m *MetricsService) RecordServiceError(service, partner, errorType string) {
	m.serviceErrorsTotal.WithLabelValues(service, partner, errorType).Inc()
}

// RecordBusinessCodeRequest records business code metrics
func (m *MetricsService) RecordBusinessCodeRequest(businessCode, service, partner string) {
	m.businessCodeRequestsTotal.WithLabelValues(businessCode, service, partner).Inc()
}

// RecordClientRequest records client-specific metrics
func (m *MetricsService) RecordClientRequest(client, service string, statusCode int) {
	statusStr := strconv.Itoa(statusCode)
	m.clientRequestsTotal.WithLabelValues(client, service, statusStr).Inc()
}

// RecordProxyLatency records proxy processing latency
func (m *MetricsService) RecordProxyLatency(service, partner, handlerType string, duration time.Duration) {
	if m.proxyLatency != nil {
		m.proxyLatency.WithLabelValues(service, partner, handlerType).Observe(duration.Seconds())
	}
}

// RecordUpstreamLatency records upstream request latency
func (m *MetricsService) RecordUpstreamLatency(service, partner, upstreamURL string, duration time.Duration) {
	if m.upstreamLatency != nil {
		m.upstreamLatency.WithLabelValues(service, partner, upstreamURL).Observe(duration.Seconds())
	}
}

// GetMetricsHandler returns the Prometheus metrics handler
func (m *MetricsService) GetMetricsHandler() http.Handler {
	return promhttp.Handler()
}
