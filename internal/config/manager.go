package config

import (
	"fmt"
	"log"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"
)

// Manager handles all configuration loading and service initialization
type Manager struct {
	appConfig        *AppConfig
	balancer         *services.Balancer
	authService      *services.AuthService
	rateLimitService *services.RateLimitService
	loggerService    *services.LoggerService
	metricsService   *services.MetricsService
	functionMap      map[string]models.Function
}

// NewManager creates a new configuration manager
func NewManager() *Manager {
	return &Manager{
		functionMap: make(map[string]models.Function),
	}
}

// Initialize loads all configurations and initializes services
func (m *Manager) Initialize() error {
	// Load configuration
	if err := m.loadConfiguration(); err != nil {
		return err
	}

	// Initialize services
	if err := m.initializeServices(); err != nil {
		return err
	}

	// Configure services
	if err := m.configureServices(); err != nil {
		return err
	}

	return nil
}

// loadConfiguration loads the new multi-file configuration
func (m *Manager) loadConfiguration() error {
	log.Println("Loading multi-file configuration from configs/ directory")

	configLoader := NewLoaderWithConfigDir("configs")

	// Load complete application configuration
	var err error
	m.appConfig, err = configLoader.LoadAppConfig()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	log.Printf("Configuration loaded successfully")
	return nil
}

// initializeServices creates all required services
func (m *Manager) initializeServices() error {
	// Initialize core services
	m.balancer = services.NewBalancer()
	m.rateLimitService = services.NewRateLimitService()
	m.metricsService = services.NewMetricsService()

	// Initialize auth service
	if err := m.initializeAuthService(); err != nil {
		return err
	}

	// Initialize logging service
	if err := m.initializeLoggingService(); err != nil {
		return err
	}

	// Initialize function configurations
	if err := m.initializeFunctionConfigs(); err != nil {
		return err
	}

	return nil
}

// initializeAuthService sets up the authentication service
func (m *Manager) initializeAuthService() error {
	if m.appConfig.Auth == nil {
		return fmt.Errorf("authentication configuration is required but not found in configs/clients.json")
	}

	m.authService = services.NewAuthService(m.appConfig.Auth)
	m.authService.SetRateLimitService(m.rateLimitService)
	log.Printf("Authentication service initialized: enabled=%v, %d clients configured",
		m.appConfig.Auth.Enabled, len(m.appConfig.Auth.Clients))

	return nil
}

// initializeLoggingService sets up the logging service
func (m *Manager) initializeLoggingService() error {
	if m.appConfig.Logging != nil {
		m.loggerService = services.NewLoggerService(m.appConfig.Logging)
		log.Printf("Logging service initialized: enabled=%v, directory=%s",
			m.appConfig.Logging.Enabled, m.appConfig.Logging.Directory)
	}
	return nil
}

// initializeFunctionConfigs sets up function configurations
func (m *Manager) initializeFunctionConfigs() error {
	if len(m.appConfig.Functions) == 0 {
		return fmt.Errorf("function configurations are required but not found in configs/functions.json")
	}

	for _, fn := range m.appConfig.Functions {
		m.functionMap[fn.Name] = fn
	}
	log.Printf("Loaded %d function configurations", len(m.appConfig.Functions))

	return nil
}

// configureServices applies configurations to services
func (m *Manager) configureServices() error {
	// Set rate limit service in balancer
	m.balancer.SetRateLimitService(m.rateLimitService)

	// Load service configurations into balancer
	for serviceName, serviceConfig := range m.appConfig.Services {
		m.balancer.LoadConfig(serviceName, &serviceConfig)
		log.Printf("Loaded config for %s with %d partners", serviceName, len(serviceConfig.Partners))
	}

	return nil
}

// GetAppConfig returns the loaded application configuration
func (m *Manager) GetAppConfig() *AppConfig {
	return m.appConfig
}

// GetBalancer returns the configured balancer service
func (m *Manager) GetBalancer() *services.Balancer {
	return m.balancer
}

// GetAuthService returns the configured auth service
func (m *Manager) GetAuthService() *services.AuthService {
	return m.authService
}

// GetRateLimitService returns the configured rate limit service
func (m *Manager) GetRateLimitService() *services.RateLimitService {
	return m.rateLimitService
}

// GetLoggerService returns the configured logger service
func (m *Manager) GetLoggerService() *services.LoggerService {
	return m.loggerService
}

// GetMetricsService returns the metrics service
func (m *Manager) GetMetricsService() *services.MetricsService {
	return m.metricsService
}

// GetFunctionMap returns the function configuration map
func (m *Manager) GetFunctionMap() map[string]models.Function {
	return m.functionMap
}

// HasLogging returns true if logging service is configured
func (m *Manager) HasLogging() bool {
	return m.loggerService != nil
}

// HasMetrics returns true if metrics are enabled
func (m *Manager) HasMetrics() bool {
	return m.metricsService != nil && m.appConfig.Metrics != nil && m.appConfig.Metrics.Enabled
}
