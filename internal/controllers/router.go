package controllers

import (
	"nova-proxy/internal/config"
	"nova-proxy/internal/handlers"
	"nova-proxy/internal/middleware"
	"nova-proxy/internal/routers"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// RouterController manages router configuration and setup
type RouterController struct {
	configManager *config.Manager
}

// NewRouterController creates a new router controller
func NewRouterController(configManager *config.Manager) *RouterController {
	return &RouterController{
		configManager: configManager,
	}
}

// SetupMainRouter configures the main API router
func (rc *RouterController) SetupMainRouter() *gin.Engine {
	// Get configured services from manager
	balancer := rc.configManager.GetBalancer()
	authService := rc.configManager.GetAuthService()
	rateLimitService := rc.configManager.GetRateLimitService()
	metricsService := rc.configManager.GetMetricsService()
	functionMap := rc.configManager.GetFunctionMap()
	loggerService := rc.configManager.GetLoggerService()

	// Initialize controllers
	proxyController := NewProxyController(rc.configManager)

	// Initialize handlers
	adminHandler := handlers.NewAdminHandler(balancer, authService)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(authService)
	rateLimitMiddleware := middleware.NewRateLimitMiddleware(authService, rateLimitService)
	metricsMiddleware := middleware.NewMetricsMiddleware(metricsService)

	// Set function configs in rate limit middleware
	if len(functionMap) > 0 {
		rateLimitMiddleware.SetFunctionConfigs(functionMap)
	}

	// Setup router
	r := gin.Default()

	// Add metrics middleware (should be first to capture all requests)
	if rc.configManager.HasMetrics() {
		r.Use(metricsMiddleware.InjectMetricsContext())
		r.Use(metricsMiddleware.CollectMetrics())
	}

	// Add logging middleware if logging is enabled
	if rc.configManager.HasLogging() {
		r.Use(middleware.LoggingMiddleware(loggerService))
	}

	// Setup proxy routes
	proxyRouter := routers.NewProxyRouter(proxyController)
	proxyRouter.SetupRoutes(r, authMiddleware, rateLimitMiddleware, metricsMiddleware, rc.configManager.HasMetrics())

	// Setup admin routes
	adminRouter := routers.NewAdminRouter(adminHandler, rateLimitMiddleware, loggerService)
	adminRouter.SetupRoutes(r, rc.configManager.HasLogging())

	return r
}

// SetupMetricsRouter configures the metrics router
func (rc *RouterController) SetupMetricsRouter() *gin.Engine {
	if !rc.configManager.HasMetrics() {
		return nil
	}

	metricsRouter := gin.New()
	metricsRouter.Use(gin.Recovery())
	metricsRouter.GET("/metrics", gin.WrapH(promhttp.Handler()))

	return metricsRouter
}

// SetupHealthRouter configures the health check router
func (rc *RouterController) SetupHealthRouter() *gin.Engine {
	healthRouter := gin.New()
	healthRouter.Use(gin.Recovery())

	// Create health controller
	healthController := NewHealthController(rc.configManager)

	// Setup health routes
	healthRouterSetup := routers.NewHealthRouter(healthController)
	healthRouterSetup.SetupRoutes(healthRouter)

	return healthRouter
}
