package controllers

import (
	"context"
	"log"
	"net/http"
	"sync"

	"nova-proxy/internal/config"
	"nova-proxy/internal/services"
)

// ServerController manages HTTP server lifecycle
type ServerController struct {
	configManager    *config.Manager
	loggerService    *services.LoggerService
	routerController *RouterController
	ctx              context.Context
	wg               *sync.WaitGroup
	servers          []*http.Server
}

// NewServerController creates a new server controller
func NewServerController(
	configManager *config.Manager,
	loggerService *services.LoggerService,
	routerController *RouterController,
	ctx context.Context,
	wg *sync.WaitGroup,
) *ServerController {
	return &ServerController{
		configManager:    configManager,
		loggerService:    loggerService,
		routerController: routerController,
		ctx:              ctx,
		wg:               wg,
		servers:          make([]*http.Server, 0),
	}
}

// Start begins all HTTP servers
func (sc *ServerController) Start() error {
	// Start main API server (port 8080)
	mainRouter := sc.routerController.SetupMainRouter()
	sc.startServer(":8080", mainRouter, "main")

	// Start metrics server (port 10001)
	if metricsRouter := sc.routerController.SetupMetricsRouter(); metricsRouter != nil {
		sc.startServer(":10001", metricsRouter, "metrics")
	}

	// Start health check server (port 11001)
	healthRouter := sc.routerController.SetupHealthRouter()
	sc.startServer(":11001", healthRouter, "health")

	return nil
}

// startServer starts a single HTTP server
func (sc *ServerController) startServer(addr string, handler http.Handler, serverType string) {
	sc.wg.Add(1)
	
	server := &http.Server{
		Addr:    addr,
		Handler: handler,
	}
	
	sc.servers = append(sc.servers, server)

	go func() {
		defer sc.wg.Done()
		log.Printf("Starting %s server on %s", serverType, addr)

		// Setup graceful shutdown for this server
		go func() {
			<-sc.ctx.Done()
			log.Printf("Shutting down %s server...", serverType)
			server.Shutdown(context.Background())
		}()

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("%s server error: %v", serverType, err)
		}
	}()
}

// Stop gracefully stops all servers
func (sc *ServerController) Stop() error {
	for _, server := range sc.servers {
		if err := server.Shutdown(context.Background()); err != nil {
			return err
		}
	}
	return nil
}

// Wait blocks until all servers finish
func (sc *ServerController) Wait() {
	sc.wg.Wait()
}
