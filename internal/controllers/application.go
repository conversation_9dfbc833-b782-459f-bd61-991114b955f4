package controllers

import (
	"context"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"nova-proxy/internal/config"
	"nova-proxy/internal/services"

	"github.com/sirupsen/logrus"
)

// ApplicationController manages the overall application lifecycle
type ApplicationController struct {
	configManager    *config.Manager
	loggerService    *services.LoggerService
	serverController *ServerController
	routerController *RouterController
	ctx              context.Context
	cancel           context.CancelFunc
	wg               sync.WaitGroup
}

// NewApplicationController creates a new application controller
func NewApplicationController() *ApplicationController {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &ApplicationController{
		ctx:    ctx,
		cancel: cancel,
	}
}

// Initialize sets up all application components
func (app *ApplicationController) Initialize() error {
	// Initialize configuration manager
	app.configManager = config.NewManager()
	if err := app.configManager.Initialize(); err != nil {
		return err
	}

	// Get logger service for structured logging
	app.loggerService = app.configManager.GetLoggerService()

	// Initialize router controller
	app.routerController = NewRouterController(app.configManager)

	// Initialize server controller
	app.serverController = NewServerController(
		app.configManager,
		app.loggerService,
		app.routerController,
		app.ctx,
		&app.wg,
	)

	return nil
}

// Start begins all application services
func (app *ApplicationController) Start() error {
	// Log server startup with structured logging
	if app.loggerService != nil {
		functionMap := app.configManager.GetFunctionMap()
		app.loggerService.LogInfo("Starting nova-proxy servers", logrus.Fields{
			"main_port":       "8080",
			"metrics_port":    "10001",
			"health_port":     "11001",
			"metrics_enabled": app.configManager.HasMetrics(),
			"logging_enabled": app.configManager.HasLogging(),
			"auth_enabled":    app.configManager.GetAuthService() != nil,
			"services_count":  len(functionMap),
		})
	}

	// Setup graceful shutdown
	app.setupGracefulShutdown()

	// Start all servers
	return app.serverController.Start()
}

// Wait blocks until all servers finish
func (app *ApplicationController) Wait() {
	app.wg.Wait()
}

// setupGracefulShutdown sets up graceful shutdown for all services
func (app *ApplicationController) setupGracefulShutdown() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Received shutdown signal, stopping all servers...")

		// Cancel context to stop all servers
		app.cancel()

		// Shutdown logging service if available
		if app.loggerService != nil {
			log.Println("Shutting down logging service...")
			app.loggerService.Shutdown()
			log.Println("Logging service shutdown complete")
		}

		log.Println("Graceful shutdown complete")
		os.Exit(0)
	}()
}
