package controllers

import (
	"fmt"
	"time"

	"nova-proxy/internal/config"

	"github.com/gin-gonic/gin"
)

// HealthController handles health check business logic
type HealthController struct {
	configManager *config.Manager
}

// NewHealthController creates a new health controller
func NewHealthController(configManager *config.Manager) *HealthController {
	return &HealthController{
		configManager: configManager,
	}
}

// GetHealth returns basic health status
func (hc *HealthController) GetHealth() gin.H {
	return gin.H{
		"status":    "healthy",
		"service":   "nova-proxy",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}
}

// GetReadiness returns readiness status with detailed checks
func (hc *HealthController) GetReadiness() (gin.H, int) {
	// Check if essential services are ready
	ready := true
	checks := gin.H{}

	// Check authentication service
	if authService := hc.configManager.GetAuthService(); authService != nil {
		checks["auth"] = "ready"
	}

	// Check metrics service
	if hc.configManager.HasMetrics() {
		checks["metrics"] = "ready"
	}

	// Check logging service
	if hc.configManager.HasLogging() {
		checks["logging"] = "ready"
	}

	// Check if we have any functions configured
	functionMap := hc.configManager.GetFunctionMap()
	if len(functionMap) > 0 {
		checks["functions"] = fmt.Sprintf("%d configured", len(functionMap))
	} else {
		ready = false
		checks["functions"] = "no functions configured"
	}

	status := "ready"
	httpStatus := 200
	if !ready {
		status = "not ready"
		httpStatus = 503
	}

	response := gin.H{
		"status":    status,
		"service":   "nova-proxy",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"checks":    checks,
	}

	return response, httpStatus
}
