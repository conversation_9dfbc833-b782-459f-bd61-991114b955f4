package controllers

import (
	"nova-proxy/internal/config"
	"nova-proxy/internal/handlers"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

// ProxyController manages proxy request handling with performance optimization
type ProxyController struct {
	configManager      *config.Manager
	standardHandler    *handlers.ProxyHandler
	streamingHandler   *handlers.StreamingProxyHandler
	useStreamingMode   bool
}

// NewProxyController creates a new proxy controller
func NewProxyController(configManager *config.Manager) *ProxyController {
	// Get configured services from manager
	balancer := configManager.GetBalancer()
	loggerService := configManager.GetLoggerService()
	
	// Initialize additional services
	proxyService := services.NewProxyService()
	debugService := services.NewDebugService()

	// Create both handlers
	standardHandler := handlers.NewProxyHandler(balancer, proxyService, debugService, loggerService)
	streamingHandler := handlers.NewStreamingProxyHandler(balancer, loggerService)

	// Determine if streaming mode should be used
	// For now, enable streaming mode by default for better performance
	useStreamingMode := true

	return &ProxyController{
		configManager:      configManager,
		standardHandler:    standardHandler,
		streamingHandler:   streamingHandler,
		useStreamingMode:   useStreamingMode,
	}
}

// HandleProxy routes requests to the appropriate handler based on configuration
func (pc *ProxyController) HandleProxy(c *gin.Context) {
	// Check content type to determine if streaming is possible
	contentType := c.GetHeader("Content-Type")
	
	// Use streaming handler for multipart requests when streaming mode is enabled
	if pc.useStreamingMode && isMultipartRequest(contentType) {
		pc.streamingHandler.HandleStreamingProxy(c)
	} else {
		// Fall back to standard handler for non-multipart or when streaming is disabled
		pc.standardHandler.HandleProxy(c)
	}
}

// isMultipartRequest checks if the request is a multipart form request
func isMultipartRequest(contentType string) bool {
	return contentType != "" && 
		   (contentType == "multipart/form-data" || 
		    len(contentType) > 19 && contentType[:19] == "multipart/form-data")
}

// SetStreamingMode enables or disables streaming mode
func (pc *ProxyController) SetStreamingMode(enabled bool) {
	pc.useStreamingMode = enabled
}

// GetStreamingMode returns the current streaming mode status
func (pc *ProxyController) GetStreamingMode() bool {
	return pc.useStreamingMode
}

// GetMetrics returns performance metrics for both handlers
func (pc *ProxyController) GetMetrics() gin.H {
	metrics := gin.H{
		"streaming_mode_enabled": pc.useStreamingMode,
		"handlers": gin.H{
			"standard":  pc.standardHandler.GetMetrics(),
			"streaming": pc.streamingHandler.GetMetrics(),
		},
	}
	
	return metrics
}
