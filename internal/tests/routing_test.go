package tests

import (
	"bytes"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"nova-proxy/internal/handlers"
	"nova-proxy/internal/middleware"
	"nova-proxy/internal/models"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTestRouter creates a test router with all middleware and handlers
func setupTestRouter(t *testing.T) (*gin.Engine, *services.Balancer, *services.AuthService) {
	gin.SetMode(gin.TestMode)

	// Create test configuration
	authConfig := &models.AuthConfig{
		Enabled: true,
		Clients: []models.Client{
			{
				Name:             "test-client",
				Token:            "test-token-123",
				AllowedFunctions: []string{"ocr", "liveness", "facematch"},
				Enabled:          true,
				GlobalRateLimit: models.RateLimitConfig{
					RequestsPerSecond: 10,
					RequestsPerMinute: 100,
				},
			},
		},
	}

	// Create test services
	balancer := services.NewBalancer()
	authService := services.NewAuthService(authConfig)
	rateLimitService := services.NewRateLimitService()
	proxyService := services.NewProxyService()
	debugService := services.NewDebugService()

	// Set up test service configuration
	setupTestServiceConfig(balancer)

	// Create handlers
	proxyHandler := handlers.NewProxyHandler(balancer, proxyService, debugService, nil)
	adminHandler := handlers.NewAdminHandler(balancer, authService)

	// Create middleware
	authMiddleware := middleware.NewAuthMiddleware(authService)
	rateLimitMiddleware := middleware.NewRateLimitMiddleware(authService, rateLimitService)

	// Setup router
	r := gin.New()

	// Service routes with authentication and rate limiting
	serviceGroup := r.Group("/")
	serviceGroup.Use(authMiddleware.Authenticate())
	serviceGroup.Use(authMiddleware.RequireService())
	serviceGroup.Use(rateLimitMiddleware.CheckRateLimits())
	serviceGroup.Use(authMiddleware.AddAuthInfo())
	serviceGroup.Use(rateLimitMiddleware.AddRateLimitHeaders())
	{
		serviceGroup.POST("/:service", proxyHandler.HandleProxy)
	}

	// Admin routes
	adminGroup := r.Group("/admin")
	{
		adminGroup.GET("/status", adminHandler.GetServiceStatus)
		adminGroup.GET("/config", adminHandler.GetConfig)
	}

	return r, balancer, authService
}

// setupTestServiceConfig creates test service configuration
func setupTestServiceConfig(balancer *services.Balancer) {
	// OCR service configuration
	ocrConfig := &models.ServiceConfig{
		Partners: []models.Partner{
			{
				Name:          "partner-a",
				BusinessCodes: []string{"banking", "finance"},
				Upstreams: []models.Upstream{
					{
						URL:    "http://partner-a.example.com/ocr",
						Format: "json",
						Weight: 10,
						FieldMapping: map[string]string{
							"img1": "image_1",
							"img2": "image_2",
						},
					},
				},
			},
			{
				Name:          "partner-b",
				BusinessCodes: []string{"insurance", "healthcare"},
				Upstreams: []models.Upstream{
					{
						URL:    "http://partner-b.example.com/api/ocr",
						Format: "multipart/form-data",
						Weight: 50,
						FieldMapping: map[string]string{
							"img1": "document_front",
							"img2": "document_back",
						},
					},
				},
			},
		},
	}

	// Liveness service configuration
	livenessConfig := &models.ServiceConfig{
		Partners: []models.Partner{
			{
				Name:          "partner-a",
				BusinessCodes: []string{"banking", "finance"},
				Upstreams: []models.Upstream{
					{
						URL:    "http://partner-a.example.com/liveness",
						Format: "json",
						Weight: 40,
					},
				},
			},
		},
	}

	// Load configurations into balancer
	balancer.LoadConfig("ocr", ocrConfig)
	balancer.LoadConfig("liveness", livenessConfig)
}

// createMultipartRequest creates a multipart form request for testing
func createMultipartRequest(method, url string, fields map[string]string) (*http.Request, error) {
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	for key, value := range fields {
		err := writer.WriteField(key, value)
		if err != nil {
			return nil, err
		}
	}

	err := writer.Close()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(method, url, &buf)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	return req, nil
}

func TestRouting_ServiceEndpoints(t *testing.T) {
	router, _, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		service        string
		expectedStatus int
		headers        map[string]string
	}{
		{
			name:           "Valid OCR service",
			service:        "ocr",
			expectedStatus: http.StatusBadRequest, // Will fail at binding stage, but routing works
			headers: map[string]string{
				"x-auth-client": "test-client",
				"x-auth-token":  "test-token-123",
			},
		},
		{
			name:           "Valid liveness service",
			service:        "liveness",
			expectedStatus: http.StatusBadRequest, // Will fail at binding stage, but routing works
			headers: map[string]string{
				"x-auth-client": "test-client",
				"x-auth-token":  "test-token-123",
			},
		},
		{
			name:           "Invalid service",
			service:        "invalid-service",
			expectedStatus: http.StatusForbidden, // Will fail at authorization stage for unknown service
			headers: map[string]string{
				"x-auth-client": "test-client",
				"x-auth-token":  "test-token-123",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := createMultipartRequest("POST", "/"+tt.service, map[string]string{
				"img1": "test-data",
				"img2": "test-data",
			})
			require.NoError(t, err)

			// Add headers
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func TestRouting_BusinessCodeRouting(t *testing.T) {
	router, _, _ := setupTestRouter(t)

	tests := []struct {
		name        string
		service     string
		bizHeader   string
		expectRoute bool
	}{
		{
			name:        "Banking business code for OCR",
			service:     "ocr",
			bizHeader:   "banking",
			expectRoute: true,
		},
		{
			name:        "Insurance business code for OCR",
			service:     "ocr",
			bizHeader:   "insurance",
			expectRoute: true,
		},
		{
			name:        "Invalid business code",
			service:     "ocr",
			bizHeader:   "invalid-biz",
			expectRoute: true, // Should still route, just use default partner selection
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := createMultipartRequest("POST", "/"+tt.service, map[string]string{
				"img1": "test-data",
				"img2": "test-data",
			})
			require.NoError(t, err)

			// Add auth headers
			req.Header.Set("x-auth-client", "test-client")
			req.Header.Set("x-auth-token", "test-token-123")
			req.Header.Set("x-biz", tt.bizHeader)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Should reach the handler (even if it fails at binding)
			assert.NotEqual(t, http.StatusNotFound, w.Code)
		})
	}
}

func TestRouting_PartnerSelection(t *testing.T) {
	router, _, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		service        string
		partnerHeader  string
		expectedStatus int
	}{
		{
			name:           "Direct partner selection - partner-a",
			service:        "ocr",
			partnerHeader:  "partner-a",
			expectedStatus: http.StatusBadRequest, // Will fail at binding, but routing works
		},
		{
			name:           "Direct partner selection - partner-b",
			service:        "ocr",
			partnerHeader:  "partner-b",
			expectedStatus: http.StatusBadRequest, // Will fail at binding, but routing works
		},
		{
			name:           "Invalid partner selection",
			service:        "ocr",
			partnerHeader:  "invalid-partner",
			expectedStatus: http.StatusBadRequest, // Should still work, fallback to default
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := createMultipartRequest("POST", "/"+tt.service, map[string]string{
				"img1": "test-data",
				"img2": "test-data",
			})
			require.NoError(t, err)

			// Add auth headers
			req.Header.Set("x-auth-client", "test-client")
			req.Header.Set("x-auth-token", "test-token-123")
			req.Header.Set("x-partner", tt.partnerHeader)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func TestRouting_AdminEndpoints(t *testing.T) {
	router, _, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		endpoint       string
		method         string
		expectedStatus int
	}{
		{
			name:           "Get service status",
			endpoint:       "/admin/status",
			method:         "GET",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Get configuration",
			endpoint:       "/admin/config",
			method:         "GET",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest(tt.method, tt.endpoint, nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			// Verify response is valid JSON
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
		})
	}
}
