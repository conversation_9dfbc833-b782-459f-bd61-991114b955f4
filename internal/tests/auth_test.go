package tests

import (
	"bytes"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"nova-proxy/internal/middleware"
	"nova-proxy/internal/models"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupAuthTestRouter creates a minimal router for authentication testing
func setupAuthTestRouter(authConfig *models.AuthConfig) *gin.Engine {
	gin.SetMode(gin.TestMode)

	authService := services.NewAuthService(authConfig)
	rateLimitService := services.NewRateLimitService()
	authMiddleware := middleware.NewAuthMiddleware(authService)
	rateLimitMiddleware := middleware.NewRateLimitMiddleware(authService, rateLimitService)

	r := gin.New()

	// Test endpoint with authentication
	authGroup := r.Group("/")
	authGroup.Use(authMiddleware.Authenticate())
	authGroup.Use(authMiddleware.RequireService())
	authGroup.Use(rateLimitMiddleware.CheckRateLimits())
	authGroup.Use(authMiddleware.AddAuthInfo())
	{
		authGroup.POST("/:service", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})
	}

	// Test endpoint without authentication
	r.GET("/public", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "public endpoint"})
	})

	return r
}

func TestAuthentication_ValidCredentials(t *testing.T) {
	authConfig := &models.AuthConfig{
		Enabled: true,
		Clients: []models.Client{
			{
				Name:             "banking-client",
				Token:            "sk-banking-1234567890abcdef",
				AllowedFunctions: []string{"ocr", "liveness"},
				Enabled:          true,
				GlobalRateLimit: models.RateLimitConfig{
					RequestsPerSecond: 10,
					RequestsPerMinute: 100,
				},
			},
			{
				Name:             "kyc-client",
				Token:            "sk-kyc-fedcba0987654321",
				AllowedFunctions: []string{"*"}, // All functions
				Enabled:          true,
				GlobalRateLimit: models.RateLimitConfig{
					RequestsPerSecond: 20,
					RequestsPerMinute: 200,
				},
			},
		},
	}

	router := setupAuthTestRouter(authConfig)

	tests := []struct {
		name           string
		client         string
		token          string
		service        string
		expectedStatus int
		expectedAuth   bool
	}{
		{
			name:           "Banking client - OCR access",
			client:         "banking-client",
			token:          "sk-banking-1234567890abcdef",
			service:        "ocr",
			expectedStatus: http.StatusOK,
			expectedAuth:   true,
		},
		{
			name:           "Banking client - Liveness access",
			client:         "banking-client",
			token:          "sk-banking-1234567890abcdef",
			service:        "liveness",
			expectedStatus: http.StatusOK,
			expectedAuth:   true,
		},
		{
			name:           "KYC client - OCR access (wildcard)",
			client:         "kyc-client",
			token:          "sk-kyc-fedcba0987654321",
			service:        "ocr",
			expectedStatus: http.StatusOK,
			expectedAuth:   true,
		},
		{
			name:           "KYC client - Facematch access (wildcard)",
			client:         "kyc-client",
			token:          "sk-kyc-fedcba0987654321",
			service:        "facematch",
			expectedStatus: http.StatusOK,
			expectedAuth:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			writer := multipart.NewWriter(&buf)
			writer.WriteField("img1", "test-data")
			writer.Close()

			req, err := http.NewRequest("POST", "/"+tt.service, &buf)
			require.NoError(t, err)

			req.Header.Set("Content-Type", writer.FormDataContentType())
			req.Header.Set("x-auth-client", tt.client)
			req.Header.Set("x-auth-token", tt.token)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedAuth {
				// Check authentication headers are present
				assert.Equal(t, tt.client, w.Header().Get("X-Authenticated-Client"))
				assert.NotEmpty(t, w.Header().Get("X-Auth-Method"))
			}
		})
	}
}

func TestAuthentication_InvalidCredentials(t *testing.T) {
	authConfig := &models.AuthConfig{
		Enabled: true,
		Clients: []models.Client{
			{
				Name:             "test-client",
				Token:            "valid-token-123",
				AllowedFunctions: []string{"ocr"},
				Enabled:          true,
			},
		},
	}

	router := setupAuthTestRouter(authConfig)

	tests := []struct {
		name           string
		client         string
		token          string
		service        string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing client header",
			client:         "",
			token:          "valid-token-123",
			service:        "ocr",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "MISSING_AUTH_HEADERS",
		},
		{
			name:           "Missing token header",
			client:         "test-client",
			token:          "",
			service:        "ocr",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "MISSING_AUTH_HEADERS",
		},
		{
			name:           "Invalid client name",
			client:         "invalid-client",
			token:          "valid-token-123",
			service:        "ocr",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "INVALID_CLIENT",
		},
		{
			name:           "Invalid token",
			client:         "test-client",
			token:          "invalid-token",
			service:        "ocr",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "INVALID_TOKEN",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			writer := multipart.NewWriter(&buf)
			writer.WriteField("img1", "test-data")
			writer.Close()

			req, err := http.NewRequest("POST", "/"+tt.service, &buf)
			require.NoError(t, err)

			req.Header.Set("Content-Type", writer.FormDataContentType())
			if tt.client != "" {
				req.Header.Set("x-auth-client", tt.client)
			}
			if tt.token != "" {
				req.Header.Set("x-auth-token", tt.token)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			// Parse response and check error
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.False(t, response["authenticated"].(bool))
			errorObj := response["error"].(map[string]interface{})
			assert.Equal(t, tt.expectedError, errorObj["code"])
		})
	}
}

func TestAuthentication_FunctionAuthorization(t *testing.T) {
	authConfig := &models.AuthConfig{
		Enabled: true,
		Clients: []models.Client{
			{
				Name:             "limited-client",
				Token:            "sk-limited-123",
				AllowedFunctions: []string{"ocr"}, // Only OCR access
				Enabled:          true,
			},
			{
				Name:             "full-client",
				Token:            "sk-full-456",
				AllowedFunctions: []string{"*"}, // All functions
				Enabled:          true,
			},
		},
	}

	router := setupAuthTestRouter(authConfig)

	tests := []struct {
		name           string
		client         string
		token          string
		service        string
		expectedStatus int
		shouldSucceed  bool
	}{
		{
			name:           "Limited client - Allowed function (OCR)",
			client:         "limited-client",
			token:          "sk-limited-123",
			service:        "ocr",
			expectedStatus: http.StatusOK,
			shouldSucceed:  true,
		},
		{
			name:           "Limited client - Forbidden function (Liveness)",
			client:         "limited-client",
			token:          "sk-limited-123",
			service:        "liveness",
			expectedStatus: http.StatusForbidden,
			shouldSucceed:  false,
		},
		{
			name:           "Limited client - Forbidden function (Facematch)",
			client:         "limited-client",
			token:          "sk-limited-123",
			service:        "facematch",
			expectedStatus: http.StatusForbidden,
			shouldSucceed:  false,
		},
		{
			name:           "Full client - Any function (OCR)",
			client:         "full-client",
			token:          "sk-full-456",
			service:        "ocr",
			expectedStatus: http.StatusOK,
			shouldSucceed:  true,
		},
		{
			name:           "Full client - Any function (Liveness)",
			client:         "full-client",
			token:          "sk-full-456",
			service:        "liveness",
			expectedStatus: http.StatusOK,
			shouldSucceed:  true,
		},
		{
			name:           "Full client - Any function (Facematch)",
			client:         "full-client",
			token:          "sk-full-456",
			service:        "facematch",
			expectedStatus: http.StatusOK,
			shouldSucceed:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			writer := multipart.NewWriter(&buf)
			writer.WriteField("img1", "test-data")
			writer.Close()

			req, err := http.NewRequest("POST", "/"+tt.service, &buf)
			require.NoError(t, err)

			req.Header.Set("Content-Type", writer.FormDataContentType())
			req.Header.Set("x-auth-client", tt.client)
			req.Header.Set("x-auth-token", tt.token)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.shouldSucceed {
				// Check success response
				var response map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Equal(t, "success", response["message"])
			} else {
				// Check error response
				var response map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Contains(t, response, "error")
				assert.Equal(t, tt.service, response["function"])
			}
		})
	}
}

func TestAuthentication_DisabledClient(t *testing.T) {
	authConfig := &models.AuthConfig{
		Enabled: true,
		Clients: []models.Client{
			{
				Name:             "disabled-client",
				Token:            "sk-disabled-123",
				AllowedFunctions: []string{"ocr"},
				Enabled:          false, // Client is disabled
			},
		},
	}

	router := setupAuthTestRouter(authConfig)

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	writer.WriteField("img1", "test-data")
	writer.Close()

	req, err := http.NewRequest("POST", "/ocr", &buf)
	require.NoError(t, err)

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("x-auth-client", "disabled-client")
	req.Header.Set("x-auth-token", "sk-disabled-123")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response["authenticated"].(bool))
	errorObj := response["error"].(map[string]interface{})
	assert.Equal(t, "CLIENT_DISABLED", errorObj["code"])
}

func TestAuthentication_DisabledAuth(t *testing.T) {
	authConfig := &models.AuthConfig{
		Enabled: false, // Authentication is globally disabled
		Clients: []models.Client{},
	}

	router := setupAuthTestRouter(authConfig)

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	writer.WriteField("img1", "test-data")
	writer.Close()

	req, err := http.NewRequest("POST", "/ocr", &buf)
	require.NoError(t, err)

	req.Header.Set("Content-Type", writer.FormDataContentType())
	// No auth headers provided

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should succeed because auth is disabled
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "success", response["message"])
}
