# Nova Proxy Test Suite

This directory contains comprehensive tests for the nova-proxy application, covering routing, authentication, and rate-limiting functionality.

## 🧪 Test Structure

### Test Files

- **`routing_test.go`** - Tests for service routing, partner selection, and business code routing
- **`auth_test.go`** - Tests for authentication, authorization, and client management
- **`ratelimit_test.go`** - Tests for rate limiting functionality and token bucket algorithms

### Test Categories

#### 1. **Routing Tests** (`routing_test.go`)
- **Service Endpoints**: Tests routing to different services (OCR, liveness, facematch)
- **Business Code Routing**: Tests routing based on `x-biz` header
- **Partner Selection**: Tests direct partner selection via `x-partner` header
- **Admin Endpoints**: Tests admin API endpoints

#### 2. **Authentication Tests** (`auth_test.go`)
- **Valid Credentials**: Tests successful authentication with correct client/token pairs
- **Invalid Credentials**: Tests authentication failures (missing headers, invalid tokens, etc.)
- **Function Authorization**: Tests function-level permissions and wildcard access
- **Disabled Clients**: Tests behavior with disabled clients
- **Disabled Authentication**: Tests behavior when authentication is globally disabled

#### 3. **Rate Limiting Tests** (`ratelimit_test.go`)
- **Client Global Limits**: Tests client-level rate limiting
- **Client Function Limits**: Tests function-specific rate limits per client
- **Function Global Limits**: Tests global function rate limits across all clients
- **Rate Limit Headers**: Tests rate limiting response headers
- **No Limits Configured**: Tests unlimited access when no limits are set
- **Token Bucket Recovery**: Tests rate limit recovery over time

## 🚀 Running Tests

### Quick Test Run
```bash
# Run all tests
go test -v ./internal/tests

# Run specific test category
go test -v ./internal/tests -run TestAuthentication
go test -v ./internal/tests -run TestRouting
go test -v ./internal/tests -run TestRateLimit
```

### Using Test Runner Script
```bash
# Run comprehensive test suite with coverage
./run_tests.sh
```

The test runner script provides:
- ✅ Colored output for easy reading
- 📊 Coverage reports (HTML format)
- 🔍 Individual test category execution
- 📁 Coverage files in `coverage/` directory

### Test Output
```
🧪 Running Nova Proxy Tests
==========================
[INFO] Go version: go version go1.24.1 linux/amd64
[INFO] Building project...
[SUCCESS] Project built successfully
[INFO] Running tests with coverage...
[SUCCESS] All tests passed
📊 Test Summary:
  ✅ Routing tests - Service endpoints, business code routing, partner selection
  ✅ Authentication tests - Valid/invalid credentials, function authorization, disabled clients
  ✅ Rate limiting tests - Client limits, function limits, token bucket recovery
```

## 🔧 Test Configuration

### Test Setup
Each test file includes helper functions for setting up test environments:

- **`setupTestRouter()`** - Creates a complete test router with all middleware
- **`setupAuthTestRouter()`** - Creates a minimal router for authentication testing
- **`setupRateLimitTestRouter()`** - Creates a router for rate limiting testing

### Test Data
Tests use realistic configuration data:

```go
// Example client configuration
authConfig := &models.AuthConfig{
    Enabled: true,
    Clients: []models.Client{
        {
            Name:             "banking-client",
            Token:            "sk-banking-1234567890abcdef",
            AllowedFunctions: []string{"ocr", "liveness"},
            Enabled:          true,
            GlobalRateLimit: models.RateLimitConfig{
                RequestsPerSecond: 10,
                RequestsPerMinute: 100,
            },
        },
    },
}
```

## 📋 Test Coverage

### Routing Tests
- ✅ Service endpoint routing (`POST /:service`)
- ✅ Business code routing (`x-biz` header)
- ✅ Direct partner selection (`x-partner` header)
- ✅ Admin API endpoints (`/admin/*`)
- ✅ Invalid service handling
- ✅ Middleware chain execution

### Authentication Tests
- ✅ Valid client authentication
- ✅ Missing authentication headers
- ✅ Invalid client names and tokens
- ✅ Function-level authorization
- ✅ Wildcard function access (`["*"]`)
- ✅ Disabled client handling
- ✅ Globally disabled authentication

### Rate Limiting Tests
- ✅ Client global rate limits (RPS/RPM)
- ✅ Client-function specific limits
- ✅ Global function rate limits
- ✅ Token bucket algorithm
- ✅ Rate limit recovery over time
- ✅ Unlimited access (no limits configured)
- ✅ Rate limit error responses

## 🛠️ Test Utilities

### Helper Functions
- **`createMultipartRequest()`** - Creates multipart form requests for testing
- **`setupTestServiceConfig()`** - Sets up test service configurations
- **Test assertions** - Uses `testify/assert` for comprehensive assertions

### Mock Data
Tests use realistic mock data that mirrors production configurations:
- Partner configurations with business codes
- Client configurations with various permission levels
- Rate limiting configurations with different limits
- Service configurations with multiple partners

## 🔍 Debugging Tests

### Verbose Output
```bash
go test -v ./internal/tests -run TestSpecificTest
```

### Test Logs
Tests include detailed logging for debugging:
```go
t.Logf("Request %d: Unexpected status %d, body: %s", i, w.Code, w.Body.String())
```

### Coverage Analysis
```bash
# Generate detailed coverage report
go test -coverprofile=coverage.out ./internal/tests
go tool cover -html=coverage.out -o coverage.html
```

## 📈 Continuous Integration

These tests are designed to be run in CI/CD pipelines:
- ✅ No external dependencies
- ✅ Fast execution (< 5 seconds)
- ✅ Deterministic results
- ✅ Comprehensive error reporting
- ✅ Coverage reporting

## 🎯 Best Practices

### Test Organization
- Tests are grouped by functionality
- Each test has a clear, descriptive name
- Test data is isolated and predictable

### Assertions
- Use specific assertions for better error messages
- Test both success and failure cases
- Verify response structure and content

### Performance
- Tests run quickly to enable frequent execution
- Rate limiting tests use minimal delays
- Mock data is lightweight but realistic

## 🚨 Common Issues

### Rate Limiting Tests
Rate limiting tests may occasionally be timing-sensitive. If tests fail intermittently:
1. Check system load during test execution
2. Verify timing assumptions in test logic
3. Consider adjusting rate limits for more predictable results

### Authentication Tests
Authentication tests depend on exact header matching:
- Ensure header names match exactly (`x-auth-client`, `x-auth-token`)
- Verify token formats match expected patterns
- Check client configuration matches test expectations
