# Build stage
FROM golang:1.25.1-alpine3.22 AS builder

WORKDIR /app

# Set Go proxy and other environment variables for better connectivity
ENV GOPROXY=https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.org
ENV CGO_ENABLED=0

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies with timeout and retry logic
RUN go mod download -x || echo "Warning: Some modules may not have downloaded, continuing with build..."

# Copy source code
COPY . .

# Build the application with vendor fallback
RUN go build -mod=mod -o nova-proxy . || \
    (echo "Trying with vendor mode..." && go mod vendor && go build -mod=vendor -o nova-proxy .)

# Final stage
FROM alpine:latest

WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/nova-proxy .

# Create directories for configs and logs
RUN mkdir -p /app/configs /app/logs

# Expose port
EXPOSE 8080

# Run the binary
CMD ["./nova-proxy"]
