# Partner Routing Documentation

Nova-proxy now supports intelligent routing to partners using multiple strategies with priority-based selection.

## Routing Strategies (Priority Order)

### 1. Direct Partner Selection (Highest Priority)
Use the `x-partner` header to route directly to a specific partner.

```bash
curl -X POST http://localhost:8080/ocr \
  -H "x-partner: partner-a" \
  -F "img1=@image1.jpg" \
  -F "img2=@image2.jpg"
```

### 2. Business Code Routing (Medium Priority)
Use the `x-biz` header to route based on business logic. The system will select from partners that handle the specified business code using weighted selection.

```bash
curl -X POST http://localhost:8080/ocr \
  -H "x-biz: banking" \
  -F "img1=@image1.jpg" \
  -F "img2=@image2.jpg"
```

### 3. Weighted Round-Robin (Default/Fallback)
When no headers are provided, the system uses weighted round-robin across all available partners.

```bash
curl -X POST http://localhost:8080/ocr \
  -F "img1=@image1.jpg" \
  -F "img2=@image2.jpg"
```

## Configuration

### Upstream Configuration Fields

```json
{
  "url": "http://partner-a.example.com/ocr",
  "format": "json",
  "weight": 30,
  "partner_id": "partner-a",
  "business_codes": ["banking", "finance"],
  "field_mapping": {
    "img1": "image_1",
    "img2": "image_2"
  },
  "hardcoded_fields": {
    "api_key": "partner-a-key"
  },
  "hardcoded_headers": {
    "Authorization": "Bearer partner-a-token",
    "X-API-Key": "partner-a-api-key",
    "X-Client-ID": "nova-proxy"
  }
}
```

### Hardcoded Headers

The `hardcoded_headers` field allows you to inject static headers into all requests to a specific upstream. This is particularly useful for:

- **Authentication**: `Authorization: Bearer token`, `X-API-Key: key`
- **Partner Identification**: `X-Client-ID: nova-proxy`, `X-Partner-ID: client-name`
- **Request Metadata**: `X-Source: nova-proxy`, `User-Agent: custom-agent`

**Example configurations:**

```json
{
  "hardcoded_headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "X-API-Key": "sk-**********abcdef",
    "X-Client-ID": "nova-proxy",
    "Content-Type": "application/json"
  }
}
```

**Header Priority:**
1. Hardcoded headers are applied **after** copying original request headers
2. Hardcoded headers will **override** any existing headers with the same name
3. Use `req.Header.Set()` to ensure hardcoded values take precedence

#### New Fields:
- **`partner_id`**: Unique identifier for the partner (used with `x-partner` header)
- **`business_codes`**: Array of business codes this partner handles (used with `x-biz` header)

## Routing Examples

### Example 1: Banking OCR Request
```bash
# Routes to partners that handle "banking" business code
curl -X POST http://localhost:8080/ocr \
  -H "x-biz: banking" \
  -H "Content-Type: multipart/form-data" \
  -F "img1=@id_front.jpg" \
  -F "img2=@id_back.jpg"
```

### Example 2: Force Specific Partner
```bash
# Always routes to partner-b regardless of business code
curl -X POST http://localhost:8080/liveness \
  -H "x-partner: partner-b" \
  -F "img1=@selfie.jpg" \
  -F "img2=@id_front.jpg" \
  -F "img3=@id_back.jpg"
```

### Example 3: Fallback to Round-Robin
```bash
# Uses weighted round-robin across all partners
curl -X POST http://localhost:8080/facematch \
  -F "img1=@photo1.jpg" \
  -F "img2=@photo2.jpg"
```

## Routing Logic Flow

1. **Check `x-partner` header**
   - If present and partner exists → Route to that partner
   - If present but partner not found → Continue to next strategy

2. **Check `x-biz` header**
   - If present, find partners with matching business code
   - Use weighted selection among matching partners
   - If no partners match → Continue to next strategy

3. **Weighted Round-Robin**
   - Use all available partners
   - Apply weighted round-robin algorithm
   - Ensures fair distribution based on partner weights

## Business Code Matching

Partners can handle multiple business codes:

```json
{
  "partner_id": "partner-a",
  "business_codes": ["banking", "finance", "insurance"]
}
```

When `x-biz: banking` is sent, this partner will be included in the candidate pool.

## Weight Distribution

Weights determine the probability of selection:

- Partner A (weight: 30) → 30% chance
- Partner B (weight: 50) → 50% chance  
- Partner C (weight: 20) → 20% chance

## Error Handling

- **Invalid partner ID**: Falls back to business code routing
- **Invalid business code**: Falls back to weighted round-robin
- **No partners available**: Returns 404 error

## Response Normalization

Nova-proxy automatically normalizes partner responses into a consistent format, regardless of which partner processes the request.

### Standard Response Format

All normalized responses follow this structure:

```json
{
  "success": true,
  "data": {
    // Service-specific normalized data
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time_ms": 1250.5,
    "request_id": "ocr-**********"
  },
  "timestamp": "2025-09-12T10:30:00Z"
}
```

### Service-Specific Normalized Responses

#### OCR Service
```json
{
  "success": true,
  "data": {
    "document_type": "id_card",
    "confidence": 0.95,
    "fields": {
      "name": "John Doe",
      "id_number": "*********",
      "date_of_birth": "1990-01-01",
      "address": "123 Main St"
    },
    "raw_text": "JOHN DOE\n*********\n01/01/1990..."
  }
}
```

#### Liveness Service
```json
{
  "success": true,
  "data": {
    "is_live": true,
    "confidence": 0.92,
    "score": 0.88,
    "checks": [
      {
        "type": "blink_detection",
        "passed": true,
        "confidence": 0.95
      }
    ]
  }
}
```

#### Face Match Service
```json
{
  "success": true,
  "data": {
    "is_match": true,
    "confidence": 0.89,
    "score": 0.89,
    "similarity": 0.91
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "INVALID_DOCUMENT",
    "message": "Document type not supported",
    "details": "Only ID cards and passports are supported"
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time_ms": 500.2,
    "request_id": "ocr-**********"
  },
  "timestamp": "2025-09-12T10:30:00Z"
}
```

## Debug Information

Debug responses now include routing information:

```json
{
  "debug_mode": true,
  "selected_upstream": {
    "partner_id": "partner-a",
    "business_codes": ["banking", "finance"],
    "routing_reason": "x-partner header"
  }
}
```
