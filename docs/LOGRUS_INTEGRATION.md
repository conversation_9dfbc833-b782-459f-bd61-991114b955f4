# Logrus Integration - Enhanced Structured Logging

## Overview

The nova-proxy application has been enhanced with **Logrus** structured logging to provide detailed, searchable, and analyzable logs. This integration maintains backward compatibility with the existing logging system while adding powerful structured logging capabilities.

## Features

### 🎯 **Structured Logging**
- **JSON Format**: All logs are output in structured JSON format for easy parsing
- **Consistent Fields**: Standardized field names across all log types
- **Rich Context**: Detailed contextual information for debugging and monitoring
- **Multiple Log Levels**: Debug, Info, Warn, Error with automatic level assignment

### 🔧 **Dedicated Loggers**
- **Application Logger**: General application events and startup information
- **Request Logger**: HTTP request details with headers, body, and metadata
- **Response Logger**: HTTP response details with status codes and timing
- **Proxy Event Logger**: Partner communication and routing events

### 📊 **Enhanced Log Details**

#### **Request Logs**
```json
{
  "timestamp": "2025-09-16T12:23:06.*********+07:00",
  "level": "info",
  "message": "HTTP Request",
  "request_id": "ocr-4ce0a557",
  "method": "POST",
  "url": "/ocr",
  "remote_addr": "127.0.0.1",
  "user_agent": "curl/7.81.0",
  "service": "ocr",
  "body_size": 28,
  "headers": {
    "Content-Type": "application/json",
    "X-Auth-Client": "banking-client"
  },
  "body": "{\"image\": \"test_image_data\"}"
}
```

#### **Response Logs**
```json
{
  "timestamp": "2025-09-16T12:23:06.*********+07:00",
  "level": "warning",
  "message": "HTTP Response",
  "request_id": "ocr-4ce0a557",
  "status_code": 400,
  "duration_ms": 15,
  "body_size": 184,
  "upstream_url": "http://partner-a.example.com/ocr",
  "partner_name": "partner-a",
  "service": "ocr",
  "error": "Validation failed"
}
```

#### **Application Logs**
```json
{
  "timestamp": "2025-09-16T12:22:26.*********+07:00",
  "level": "info",
  "message": "Starting nova-proxy server",
  "port": "8080",
  "metrics_enabled": true,
  "logging_enabled": true,
  "auth_enabled": true,
  "services_count": 3
}
```

#### **Error Logs**
```json
{
  "timestamp": "2025-09-16T12:25:30.*********+07:00",
  "level": "error",
  "message": "Failed to contact partner",
  "service": "ocr",
  "partner_name": "partner-a",
  "upstream_url": "http://partner-a.example.com/ocr",
  "client_ip": "127.0.0.1",
  "duration_ms": 5000,
  "error": "context deadline exceeded"
}
```

## Configuration

### **Log Files**
- `logs/application.log` - General application events
- `logs/requests.log` - HTTP request details
- `logs/responses.log` - HTTP response details  
- `logs/proxy_events.log` - Partner communication events

### **Log Levels**
- **Debug**: Detailed debugging information
- **Info**: General operational messages
- **Warn**: Warning conditions (4xx responses)
- **Error**: Error conditions (5xx responses, failures)

### **Automatic Level Assignment**
- **Responses**: 
  - 2xx-3xx → Info
  - 4xx → Warning  
  - 5xx → Error
- **Partner Failures**: Error level with detailed context
- **Routing Issues**: Warning level with routing details

## Benefits

### 🔍 **Enhanced Debugging**
- **Request Tracing**: Follow requests through the entire pipeline
- **Error Context**: Rich error information with stack traces
- **Performance Monitoring**: Response times and bottleneck identification
- **Partner Analytics**: Partner performance and failure analysis

### 📈 **Operational Intelligence**
- **Client Usage Patterns**: Track client behavior and usage
- **Service Performance**: Monitor service-level metrics
- **Business Code Analytics**: Analyze routing patterns
- **Rate Limiting Insights**: Monitor rate limit hits and patterns

### 🛠 **Integration Ready**
- **ELK Stack**: Direct integration with Elasticsearch, Logstash, Kibana
- **Prometheus**: Complement metrics with detailed log analysis
- **Grafana**: Create dashboards combining metrics and logs
- **Log Aggregation**: Easy integration with centralized logging systems

## Usage Examples

### **Searching Logs**
```bash
# Find all errors for a specific service
grep '"service":"ocr"' logs/application.log | grep '"level":"error"'

# Find requests from specific client
grep '"X-Auth-Client":"banking-client"' logs/requests.log

# Find slow responses (>1000ms)
grep -E '"duration_ms":[0-9]{4,}' logs/responses.log
```

### **Log Analysis**
```bash
# Count requests by service
grep -o '"service":"[^"]*"' logs/requests.log | sort | uniq -c

# Find most common errors
grep '"level":"error"' logs/application.log | grep -o '"message":"[^"]*"' | sort | uniq -c

# Monitor partner performance
grep '"partner_name":"partner-a"' logs/responses.log | grep -o '"duration_ms":[0-9]*'
```

## Implementation Details

### **Dual Logging System**
- **Traditional Logging**: Maintains existing file-based logging for compatibility
- **Structured Logging**: New Logrus-based structured logging for enhanced analysis
- **Asynchronous Processing**: Both systems use background workers for performance

### **Performance Optimizations**
- **Dedicated Loggers**: Separate logger instances for different log types
- **Buffered Channels**: Asynchronous logging to prevent blocking
- **File Rotation**: Automatic log file rotation and compression
- **Memory Efficient**: Minimal memory overhead with buffer pooling

### **Error Handling**
- **Graceful Degradation**: Logging failures don't affect application performance
- **Fallback Mechanisms**: Stdout fallback if file logging fails
- **Resource Management**: Proper cleanup and resource management

## Migration Notes

### **Backward Compatibility**
- ✅ Existing log configuration continues to work
- ✅ Traditional log files are still generated
- ✅ No breaking changes to existing functionality
- ✅ All tests continue to pass

### **New Capabilities**
- ✅ Structured JSON logs for machine parsing
- ✅ Enhanced error context and debugging information
- ✅ Automatic log level assignment based on response codes
- ✅ Rich metadata for operational intelligence

The enhanced logging system provides enterprise-grade observability while maintaining full backward compatibility with existing configurations and workflows.
