# Authentication System

Nova-proxy implements a comprehensive client-based authentication system with function-level permissions and rate limiting.

## 🔐 Authentication Overview

### Client-Based Authentication
- Each client has a unique name and token
- Clients authenticate using HTTP headers: `x-auth-client` and `x-auth-token`
- Function-level permissions control which services clients can access
- Built-in rate limiting prevents abuse

### Authentication Headers
```http
x-auth-client: banking-client
x-auth-token: sk-banking-1234567890abcdef
```

## 🎯 Client Configuration

### Client Structure
```json
{
  "name": "banking-client",
  "token": "sk-banking-1234567890abcdef",
  "allowed_functions": ["ocr", "liveness"],
  "enabled": true,
  "description": "Banking application client",
  "rate_limit": 100,
  "created_at": "2025-09-15T15:00:00Z"
}
```

### Function Permissions
- **Specific Functions**: `["ocr", "liveness"]` - Client can only call these services
- **All Functions**: `["*"]` - <PERSON><PERSON> has access to all services
- **No Access**: `[]` - <PERSON><PERSON> cannot call any services

### Rate Limiting
- `rate_limit: 100` - Maximum 100 requests per minute
- `rate_limit: 0` - Unlimited requests
- Rate limits are enforced per client

## 📋 Configuration File

### auth-config.json
```json
{
  "enabled": true,
  "clients": [
    {
      "name": "banking-client",
      "token": "sk-banking-1234567890abcdef",
      "allowed_functions": ["ocr", "liveness"],
      "enabled": true,
      "description": "Banking application client",
      "rate_limit": 100
    },
    {
      "name": "kyc-client",
      "token": "sk-kyc-fedcba0987654321",
      "allowed_functions": ["*"],
      "enabled": true,
      "description": "KYC service with full access",
      "rate_limit": 200
    }
  ]
}
```

## 🚀 Usage Examples

### Authenticated Request
```bash
curl -X POST http://localhost:8080/ocr \
  -H "x-auth-client: banking-client" \
  -H "x-auth-token: sk-banking-1234567890abcdef" \
  -F "img1=@image1.jpg" \
  -F "img2=@image2.jpg"
```

### Response with Authentication Info
```json
{
  "success": true,
  "data": {
    "document_type": "passport",
    "confidence": 0.95
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time": 1250.5,
    "request_id": "req-123"
  }
}
```

**Response Headers:**
```http
X-Authenticated-Client: banking-client
X-Auth-Method: header
```

## ❌ Error Responses

### Missing Authentication Headers
```json
{
  "error": {
    "code": "MISSING_AUTH_HEADERS",
    "message": "Missing authentication headers: x-auth-client and x-auth-token required"
  },
  "authenticated": false
}
```

### Invalid Client
```json
{
  "error": {
    "code": "INVALID_CLIENT",
    "message": "Client 'unknown-client' not found"
  },
  "authenticated": false
}
```

### Invalid Token
```json
{
  "error": {
    "code": "INVALID_TOKEN",
    "message": "Invalid authentication token"
  },
  "authenticated": false
}
```

### Access Denied
```json
{
  "error": {
    "code": "ACCESS_DENIED",
    "message": "Client 'banking-client' is not authorized to call function 'facematch'",
    "details": "Allowed functions: ocr, liveness"
  },
  "function": "facematch"
}
```

### Rate Limit Exceeded
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded: 100 requests per minute"
  },
  "authenticated": false
}
```

### Client Disabled
```json
{
  "error": {
    "code": "CLIENT_DISABLED",
    "message": "Client 'test-client' is disabled"
  },
  "authenticated": false
}
```

## 🔧 Admin API

### List All Clients
```bash
GET /admin/clients
```

**Response:**
```json
{
  "message": "Clients retrieved successfully",
  "clients": [
    {
      "name": "banking-client",
      "allowed_functions": ["ocr", "liveness"],
      "enabled": true,
      "description": "Banking application client",
      "rate_limit": 100,
      "created_at": "2025-09-15T15:00:00Z",
      "last_used": "2025-09-15T16:30:00Z"
    }
  ],
  "count": 1
}
```

### Get Specific Client
```bash
GET /admin/clients/banking-client
```

**Response:**
```json
{
  "message": "Client retrieved successfully",
  "client": {
    "name": "banking-client",
    "allowed_functions": ["ocr", "liveness"],
    "enabled": true,
    "description": "Banking application client",
    "rate_limit": 100,
    "created_at": "2025-09-15T15:00:00Z",
    "last_used": "2025-09-15T16:30:00Z"
  }
}
```

## 🛡️ Security Features

### 1. **Token-Based Authentication**
- Secure token validation for each request
- Tokens are never exposed in responses or logs

### 2. **Function-Level Authorization**
- Granular permissions per client
- Wildcard support for full access

### 3. **Rate Limiting**
- Per-client rate limiting
- Sliding window implementation
- Configurable limits per client

### 4. **Client Management**
- Enable/disable clients without changing tokens
- Track last usage timestamps
- Detailed client descriptions

### 5. **Debug Information**
- Authentication details in debug responses
- No sensitive data exposure
- Request tracking and auditing

## ⚙️ Configuration Options

### Global Settings
```json
{
  "enabled": true,  // Enable/disable authentication globally
  "clients": [...]  // Client configurations
}
```

### Client Settings
- `name`: Unique client identifier
- `token`: Authentication token (keep secure)
- `allowed_functions`: Array of permitted functions or ["*"] for all
- `enabled`: Whether client is active
- `description`: Human-readable description
- `rate_limit`: Requests per minute (0 = unlimited)

## 🔄 Authentication Flow

1. **Request Received**: Client sends request with auth headers
2. **Header Extraction**: Extract `x-auth-client` and `x-auth-token`
3. **Client Lookup**: Find client by name in configuration
4. **Token Validation**: Verify token matches client's token
5. **Status Check**: Ensure client is enabled
6. **Rate Limiting**: Check if client is within rate limits
7. **Function Authorization**: Verify client can call requested function
8. **Request Processing**: Forward to upstream partner
9. **Response Enhancement**: Add auth info to response headers

This authentication system provides enterprise-grade security with flexible permissions and comprehensive monitoring! 🚀
