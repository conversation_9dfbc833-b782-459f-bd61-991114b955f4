# Grafana Monitoring Setup for Nova Proxy

This document describes the complete monitoring setup using Grafana, Prometheus, and Docker Compose to visualize metrics from the Nova Proxy API.

## 🚀 Quick Start

### 1. Start the Monitoring Stack

```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f nova-proxy
docker-compose logs -f prometheus
docker-compose logs -f grafana
```

### 2. Access the Services

- **Nova Proxy API**: http://localhost:8080
- **Prometheus**: http://localhost:9090
- **<PERSON>ana**: http://localhost:3000
  - Username: `admin`
  - Password: `admin123`

### 3. Verify Metrics Collection

```bash
# Check if metrics are being collected
curl http://localhost:8080/metrics

# Check Prometheus targets
# Go to http://localhost:9090/targets
```

## 📊 Dashboard Overview

The Nova Proxy Dashboard includes the following panels:

### Core Request Metrics
- **Request Rate by Endpoint**: Shows requests per second for each endpoint with status codes
- **Requests in Flight**: Current number of active requests being processed
- **Request Duration Percentiles**: 95th and 50th percentile response times

### Authentication & Security
- **Authentication Requests**: Authentication success/failure rates by client
- **Rate Limit Checks**: Rate limiting activity by client and function

### Service & Partner Analytics
- **Service Requests by Partner**: Request distribution across different partners
- **Service Errors**: Error tracking by type, service, and partner
- **Upstream Request Duration**: Performance metrics for partner API calls

### Business Intelligence
- **Business Code Requests**: Request patterns by business code and service
- **Client Request Analytics**: Client usage patterns with status code breakdown

## 🔧 Configuration Files

### Docker Compose Structure
```
├── docker-compose.yml          # Main orchestration file
├── monitoring/
│   ├── prometheus.yml          # Prometheus configuration
│   └── grafana/
│       ├── provisioning/
│       │   ├── datasources/    # Auto-configured data sources
│       │   └── dashboards/     # Dashboard provisioning
│       └── dashboards/
│           └── nova-proxy-dashboard.json  # Main dashboard
```

### Key Metrics Available

Based on the metrics template, the following metrics are collected:

#### Request Metrics
- `nova_proxy_requests_total` - Total HTTP requests by endpoint, method, status
- `nova_proxy_request_duration_seconds` - Request duration histograms
- `nova_proxy_request_size_bytes` - Request size histograms
- `nova_proxy_requests_in_flight` - Current active requests

#### Authentication Metrics
- `nova_proxy_auth_requests_total` - Authentication attempts by client and result

#### Service Metrics
- `nova_proxy_service_requests_total` - Service requests by client, partner, service
- `nova_proxy_service_errors_total` - Service errors by type, partner, service
- `nova_proxy_service_request_duration_seconds` - Service-specific duration

#### Rate Limiting Metrics
- `nova_proxy_rate_limit_checks_total` - Rate limit checks by client, function, result

#### Business Metrics
- `nova_proxy_business_code_requests_total` - Requests by business code, partner, service
- `nova_proxy_client_requests_total` - Client analytics by service and status

#### Upstream Metrics
- `nova_proxy_upstream_requests_total` - Upstream requests by partner, service, status
- `nova_proxy_upstream_responses_total` - Upstream responses with success tracking
- `nova_proxy_upstream_request_duration_seconds` - Upstream API performance

## 🛠 Customization

### Adding New Panels

1. Access Grafana at http://localhost:3000
2. Navigate to the Nova Proxy Dashboard
3. Click "Add Panel" to create new visualizations
4. Use the available metrics to create custom queries

### Example Queries

```promql
# Success rate by service
rate(nova_proxy_requests_total{status_code=~"2.."}[5m]) / rate(nova_proxy_requests_total[5m])

# Error rate by client
rate(nova_proxy_client_requests_total{status_code=~"4..|5.."}[5m])

# Average response time by endpoint
rate(nova_proxy_request_duration_seconds_sum[5m]) / rate(nova_proxy_request_duration_seconds_count[5m])

# Top clients by request volume
topk(10, rate(nova_proxy_client_requests_total[5m]))
```

## 🔍 Troubleshooting

### Common Issues

1. **Grafana shows "No data"**
   - Check if Prometheus is scraping metrics: http://localhost:9090/targets
   - Verify nova-proxy is exposing metrics: http://localhost:8080/metrics

2. **Prometheus not collecting metrics**
   - Check nova-proxy service is accessible from prometheus container
   - Verify prometheus.yml configuration

3. **Dashboard not loading**
   - Check Grafana logs: `docker-compose logs grafana`
   - Verify dashboard JSON syntax

### Useful Commands

```bash
# Restart specific service
docker-compose restart nova-proxy

# View real-time logs
docker-compose logs -f --tail=100 nova-proxy

# Access container shell
docker-compose exec nova-proxy sh
docker-compose exec prometheus sh
docker-compose exec grafana sh

# Clean up and restart
docker-compose down -v
docker-compose up -d
```

## 📈 Performance Monitoring

The dashboard provides comprehensive monitoring for:

- **Throughput**: Request rates and volume trends
- **Latency**: Response time percentiles and distributions  
- **Errors**: Error rates and types across services
- **Capacity**: Resource utilization and rate limiting
- **Business Metrics**: Client usage and business code analytics

This setup enables proactive monitoring, alerting, and performance optimization for the Nova Proxy application.
