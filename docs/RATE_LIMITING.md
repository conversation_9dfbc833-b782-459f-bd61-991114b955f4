# Advanced Rate Limiting System

Nova-proxy now includes a comprehensive multi-level rate limiting system that provides fine-grained control over request rates while maintaining automatic partner failover capabilities.

## 🎯 Rate Limiting Levels

### 1. **Client-Level Rate Limiting**
- **Global Client Limits**: Overall rate limits per client across all functions
- **Client-Function Limits**: Specific rate limits per client per function

### 2. **Function-Level Rate Limiting**
- **Global Function Limits**: Overall rate limits per function across all clients

### 3. **Partner-Level Rate Limiting**
- **Global Partner Limits**: Overall rate limits per partner across all functions
- **Partner-Function Limits**: Specific rate limits per partner per function

## 🚀 Rate Limit Types

### Requests Per Second (RPS)
- Short-term burst protection using token bucket algorithm
- Prevents sudden spikes in traffic
- Refills tokens at a constant rate

### Requests Per Minute (RPM)
- Longer-term capacity management using sliding window algorithm
- Ensures sustained load doesn't exceed partner capacity
- Tracks requests over a rolling 60-second window

## ⚡ Automatic Partner Failover

When a partner hits rate limits:
1. **Intelligent Selection**: System automatically routes to available partners
2. **Business Code Routing**: Respects business code preferences while avoiding rate-limited partners
3. **Graceful Degradation**: Falls back to any available partner if all preferred partners are rate-limited
4. **Real-time Monitoring**: Continuously monitors partner availability

## 📋 Configuration

### Client Configuration (`auth-config.json`)

```json
{
  "enabled": true,
  "clients": [
    {
      "name": "banking-client",
      "token": "sk-banking-1234567890abcdef",
      "allowed_functions": ["ocr", "liveness"],
      "enabled": true,
      "rate_limit": 100,  // Legacy: RPM limit
      "global_rate_limit": {
        "requests_per_second": 10,
        "requests_per_minute": 500
      },
      "function_rate_limits": [
        {
          "client_name": "banking-client",
          "function_name": "ocr",
          "rate_limit": {
            "requests_per_second": 5,
            "requests_per_minute": 200
          }
        }
      ]
    }
  ]
}
```

### Partner Configuration (`config.json`)

```json
{
  "services": {
    "ocr": {
      "partners": [
        {
          "name": "partner-a",
          "business_codes": ["banking", "finance"],
          "global_rate_limit": {
            "requests_per_second": 20,
            "requests_per_minute": 1000
          },
          "function_rate_limits": [
            {
              "partner_name": "partner-a",
              "function_name": "ocr",
              "rate_limit": {
                "requests_per_second": 15,
                "requests_per_minute": 600
              }
            }
          ],
          "upstreams": [...]
        }
      ]
    }
  }
}
```

### Function Configuration (`function-config.json`)

```json
{
  "functions": [
    {
      "name": "ocr",
      "service": "ocr",
      "description": "Optical Character Recognition service",
      "requires_auth": true,
      "rate_limit": {
        "requests_per_second": 50,
        "requests_per_minute": 2000
      }
    }
  ]
}
```

## 🔧 API Endpoints

### Get Rate Limiting Statistics
```bash
GET /admin/ratelimit/stats
```

Response:
```json
{
  "message": "Rate limiting statistics retrieved successfully",
  "stats": {
    "client_stats": {
      "banking-client": {
        "rps_tokens_remaining": 8,
        "rpm_requests_in_window": 45,
        "rps_limit": 10,
        "rpm_limit": 500
      }
    },
    "function_stats": {
      "ocr": {
        "rps_tokens_remaining": 35,
        "rpm_requests_in_window": 1250,
        "rps_limit": 50,
        "rpm_limit": 2000
      }
    },
    "partner_stats": {
      "partner-a": {
        "rps_tokens_remaining": 12,
        "rpm_requests_in_window": 450,
        "rps_limit": 20,
        "rpm_limit": 1000
      }
    }
  }
}
```

### Reset Rate Limiter
```bash
POST /admin/ratelimit/reset
Content-Type: application/json

{
  "limiter_type": "client",
  "key": "banking-client"
}
```

Available limiter types:
- `client`: Reset client global rate limiter
- `client-function`: Reset client-function rate limiter (key: "client:function")
- `function`: Reset function global rate limiter
- `partner`: Reset partner global rate limiter
- `partner-function`: Reset partner-function rate limiter (key: "partner:function")

## 📊 Response Headers

Rate limiting information is included in response headers:

```http
X-RateLimit-Checked: true
X-RateLimit-Function: ocr
X-RateLimit-Partner: partner-a
X-Authenticated-Client: banking-client
```

## ⚠️ Error Responses

When rate limits are exceeded:

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Client-function rate limit exceeded for banking-client:ocr - 5 RPS, 200 RPM",
    "type": "client_function_rate_limit"
  },
  "rate_limited": true
}
```

Error types:
- `client_function_rate_limit`: Client-function specific limit exceeded
- `function_rate_limit`: Global function limit exceeded
- `partner_rate_limit`: Partner global limit exceeded
- `partner_function_rate_limit`: Partner-function specific limit exceeded

## 🔄 Request Flow

1. **Authentication**: Client authenticates with headers
2. **Client Rate Check**: Check client global and function-specific limits
3. **Function Rate Check**: Check global function limits
4. **Partner Selection**: Select available (non-rate-limited) partner
5. **Partner Rate Check**: Verify partner can handle the request
6. **Request Processing**: Forward to selected partner
7. **Response Enhancement**: Add rate limiting headers

## 🛡️ Backward Compatibility

The system maintains full backward compatibility:
- Legacy `rate_limit` field in client config still works
- Existing authentication and authorization logic preserved
- All hardcoded headers functionality maintained
- Original partner selection logic available as fallback

This advanced rate limiting system provides enterprise-grade traffic management while ensuring high availability through intelligent failover! 🚀
