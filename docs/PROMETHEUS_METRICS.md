# Prometheus Metrics Documentation

Nova Proxy provides comprehensive Prometheus metrics for monitoring request patterns, authentication, rate limiting, and performance. This document describes all available metrics and their usage.

## Overview

The metrics endpoint is available at `/metrics` and provides real-time monitoring data in Prometheus format. Metrics are automatically collected by middleware and provide insights into:

- **Request Patterns** - HTTP request counts, durations, and sizes
- **Authentication** - Success/failure rates and client activity
- **Rate Limiting** - Rate limit hits and enforcement statistics
- **Service Performance** - Service-specific metrics and error rates
- **Business Intelligence** - Client and business code analytics

## Configuration

Metrics are configured in `configs/metrics.json`:

```json
{
  "enabled": true,
  "endpoint": "/metrics",
  "namespace": "nova_proxy",
  "collection": {
    "request_metrics": true,
    "auth_metrics": true,
    "rate_limit_metrics": true,
    "upstream_metrics": true,
    "service_metrics": true,
    "business_metrics": true
  }
}
```

## Available Metrics

### 1. Request Metrics

#### `nova_proxy_requests_total`
**Type:** Counter  
**Description:** Total number of HTTP requests processed  
**Labels:** `method`, `endpoint`, `status_code`

```prometheus
nova_proxy_requests_total{endpoint="/:service",method="POST",status_code="200"} 1250
nova_proxy_requests_total{endpoint="/admin/status",method="GET",status_code="200"} 45
```

#### `nova_proxy_request_duration_seconds`
**Type:** Histogram  
**Description:** HTTP request duration in seconds  
**Labels:** `method`, `endpoint`, `status_code`

```prometheus
nova_proxy_request_duration_seconds_bucket{endpoint="/:service",method="POST",status_code="200",le="0.1"} 1200
nova_proxy_request_duration_seconds_sum{endpoint="/:service",method="POST",status_code="200"} 45.2
```

#### `nova_proxy_requests_in_flight`
**Type:** Gauge  
**Description:** Number of HTTP requests currently being processed

```prometheus
nova_proxy_requests_in_flight 12
```

#### `nova_proxy_request_size_bytes`
**Type:** Histogram  
**Description:** HTTP request size in bytes  
**Labels:** `method`, `endpoint`

#### `nova_proxy_response_size_bytes`
**Type:** Histogram  
**Description:** HTTP response size in bytes  
**Labels:** `method`, `endpoint`, `status_code`

### 2. Authentication Metrics

#### `nova_proxy_auth_requests_total`
**Type:** Counter  
**Description:** Total number of authentication requests  
**Labels:** `client`, `result`

```prometheus
nova_proxy_auth_requests_total{client="banking-client",result="success"} 1200
nova_proxy_auth_requests_total{client="banking-client",result="failure"} 15
```

#### `nova_proxy_auth_failures_total`
**Type:** Counter  
**Description:** Total number of authentication failures  
**Labels:** `client`, `reason`

```prometheus
nova_proxy_auth_failures_total{client="banking-client",reason="invalid_token"} 8
nova_proxy_auth_failures_total{client="unknown-client",reason="unauthorized"} 25
```

### 3. Rate Limiting Metrics

#### `nova_proxy_rate_limit_hits_total`
**Type:** Counter  
**Description:** Total number of rate limit hits  
**Labels:** `client`, `function`, `limit_type`

```prometheus
nova_proxy_rate_limit_hits_total{client="banking-client",function="ocr",limit_type="client_function"} 45
nova_proxy_rate_limit_hits_total{client="kyc-client",function="facematch",limit_type="function_global"} 12
```

#### `nova_proxy_rate_limit_checks_total`
**Type:** Counter  
**Description:** Total number of rate limit checks performed  
**Labels:** `client`, `function`, `result`

```prometheus
nova_proxy_rate_limit_checks_total{client="banking-client",function="ocr",result="pass"} 1200
nova_proxy_rate_limit_checks_total{client="banking-client",function="ocr",result="hit"} 45
```

### 4. Service Metrics

#### `nova_proxy_service_requests_total`
**Type:** Counter  
**Description:** Total number of requests per service  
**Labels:** `service`, `client`, `partner`

```prometheus
nova_proxy_service_requests_total{service="ocr",client="banking-client",partner="partner-a"} 800
nova_proxy_service_requests_total{service="liveness",client="kyc-client",partner="partner-b"} 450
```

#### `nova_proxy_service_request_duration_seconds`
**Type:** Histogram  
**Description:** Service request duration in seconds  
**Labels:** `service`, `partner`

#### `nova_proxy_service_errors_total`
**Type:** Counter  
**Description:** Total number of service errors  
**Labels:** `service`, `partner`, `error_type`

```prometheus
nova_proxy_service_errors_total{service="ocr",partner="partner-a",error_type="bad_request"} 25
nova_proxy_service_errors_total{service="facematch",partner="partner-c",error_type="timeout"} 8
```

### 5. Upstream/Partner Metrics

#### `nova_proxy_upstream_requests_total`
**Type:** Counter  
**Description:** Total number of requests sent to upstream partners  
**Labels:** `partner`, `service`, `status_code`

#### `nova_proxy_upstream_request_duration_seconds`
**Type:** Histogram  
**Description:** Duration of requests to upstream partners  
**Labels:** `partner`, `service`

#### `nova_proxy_upstream_responses_total`
**Type:** Counter  
**Description:** Total number of responses received from upstream partners  
**Labels:** `partner`, `service`, `status_code`, `success`

### 6. Business Metrics

#### `nova_proxy_business_code_requests_total`
**Type:** Counter  
**Description:** Total number of requests per business code  
**Labels:** `business_code`, `service`, `partner`

```prometheus
nova_proxy_business_code_requests_total{business_code="BANK001",service="ocr",partner="partner-a"} 500
nova_proxy_business_code_requests_total{business_code="KYC002",service="facematch",partner="partner-b"} 200
```

#### `nova_proxy_client_requests_total`
**Type:** Counter  
**Description:** Total number of requests per client  
**Labels:** `client`, `service`, `status_code`

```prometheus
nova_proxy_client_requests_total{client="banking-client",service="ocr",status_code="200"} 1200
nova_proxy_client_requests_total{client="kyc-client",service="liveness",status_code="400"} 15
```

## Usage Examples

### Grafana Queries

**Request Rate:**
```promql
rate(nova_proxy_requests_total[5m])
```

**Error Rate:**
```promql
rate(nova_proxy_requests_total{status_code=~"4..|5.."}[5m]) / rate(nova_proxy_requests_total[5m])
```

**Average Response Time:**
```promql
rate(nova_proxy_request_duration_seconds_sum[5m]) / rate(nova_proxy_request_duration_seconds_count[5m])
```

**Rate Limit Hit Rate:**
```promql
rate(nova_proxy_rate_limit_hits_total[5m])
```

**Top Clients by Request Volume:**
```promql
topk(10, sum by (client) (rate(nova_proxy_client_requests_total[5m])))
```

### Alerting Rules

**High Error Rate:**
```yaml
- alert: HighErrorRate
  expr: rate(nova_proxy_requests_total{status_code=~"5.."}[5m]) > 0.1
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "High error rate detected"
```

**Rate Limiting Threshold:**
```yaml
- alert: HighRateLimitHits
  expr: rate(nova_proxy_rate_limit_hits_total[5m]) > 10
  for: 1m
  labels:
    severity: warning
  annotations:
    summary: "High rate limit hits detected"
```

## Testing

Use the provided test script to generate sample metrics:

```bash
./test_metrics.sh
```

This script exercises all metric types and provides a comprehensive view of the monitoring capabilities.

## Integration

The metrics endpoint can be scraped by Prometheus using this configuration:

```yaml
scrape_configs:
  - job_name: 'nova-proxy'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s
```
