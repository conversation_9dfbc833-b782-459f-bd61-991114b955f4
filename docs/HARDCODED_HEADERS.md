# Hardcoded Headers Feature

The `hardcoded_headers` feature allows you to inject static HTTP headers into all requests sent to specific upstream partners. This is essential for partner authentication, API key management, and request metadata.

## 🎯 Use Cases

### 1. **Authentication Headers**
```json
{
  "hardcoded_headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "X-API-Key": "sk-**********abcdef"
  }
}
```

### 2. **Partner Identification**
```json
{
  "hardcoded_headers": {
    "X-Client-ID": "nova-proxy",
    "X-Partner-ID": "banking-client",
    "User-Agent": "nova-proxy/1.0 (banking-integration)"
  }
}
```

### 3. **Request Metadata**
```json
{
  "hardcoded_headers": {
    "X-Environment": "production",
    "X-Source": "nova-proxy",
    "X-Request-ID": "auto-generated",
    "Accept": "application/json"
  }
}
```

### 4. **Webhook & Callback URLs**
```json
{
  "hardcoded_headers": {
    "X-Webhook-URL": "https://nova-proxy.com/webhooks/partner-a",
    "X-Callback-URL": "https://nova-proxy.com/callbacks/results"
  }
}
```

## 🔧 Configuration

### Basic Configuration
```json
{
  "name": "partner-example",
  "upstreams": [
    {
      "url": "https://api.partner.com/v1/service",
      "format": "json",
      "weight": 100,
      "hardcoded_headers": {
        "Authorization": "Bearer your-token-here",
        "X-API-Key": "your-api-key-here"
      }
    }
  ]
}
```

### Advanced Configuration with Multiple Headers
```json
{
  "hardcoded_headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.token",
    "X-API-Key": "sk-live-**********abcdef",
    "X-Client-ID": "nova-proxy",
    "X-Environment": "production",
    "X-Model-Version": "v2.1",
    "User-Agent": "nova-proxy/1.0 (client-integration)",
    "Accept": "application/json",
    "Content-Type": "application/json",
    "X-Webhook-URL": "https://nova-proxy.com/webhooks/results",
    "X-Rate-Limit-Bypass": "premium-client"
  }
}
```

## 🔄 Header Processing Order

1. **Original Request Headers**: Headers from the client request are copied first
2. **Hardcoded Headers**: Static headers from configuration are applied
3. **Header Override**: Hardcoded headers will **override** any existing headers with the same name

```go
// Copy headers from original request
for key, values := range c.Request.Header {
    if key != "Content-Length" {
        for _, value := range values {
            req.Header.Add(key, value)
        }
    }
}

// Add hardcoded headers (these will override existing headers)
for key, value := range upstream.HardcodedHeaders {
    req.Header.Set(key, value)  // Set() replaces existing values
}
```

## 🛡️ Security Best Practices

### 1. **Environment Variables for Secrets**
Instead of hardcoding sensitive values in config files:

```bash
# Environment variables
export PARTNER_A_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
export PARTNER_A_API_KEY="sk-live-**********abcdef"
```

```json
{
  "hardcoded_headers": {
    "Authorization": "Bearer ${PARTNER_A_TOKEN}",
    "X-API-Key": "${PARTNER_A_API_KEY}"
  }
}
```

### 2. **Separate Configs by Environment**
- `config-production.json` - Production API keys
- `config-staging.json` - Staging API keys  
- `config-development.json` - Development API keys

### 3. **Rotate Keys Regularly**
- Use short-lived tokens when possible
- Implement key rotation procedures
- Monitor for unauthorized usage

## 📊 Debug Information

When debug mode is enabled, hardcoded headers are included in the debug response:

```json
{
  "selected_upstream": {
    "partner_name": "partner-a",
    "upstream_url": "https://api.partner-a.com/v1/ocr",
    "hardcoded_headers": {
      "Authorization": "Bearer [REDACTED]",
      "X-API-Key": "[REDACTED]",
      "X-Client-ID": "nova-proxy"
    }
  }
}
```

## 🔍 Common Header Examples

### Authentication
```json
{
  "Authorization": "Bearer token",
  "Authorization": "Basic base64credentials",
  "Authorization": "ApiKey your-api-key",
  "X-API-Key": "your-api-key",
  "X-Auth-Token": "your-auth-token"
}
```

### Partner Identification
```json
{
  "X-Client-ID": "nova-proxy",
  "X-Partner-ID": "banking-client",
  "X-Tenant-ID": "your-tenant-id",
  "User-Agent": "nova-proxy/1.0"
}
```

### Request Metadata
```json
{
  "X-Environment": "production",
  "X-Source": "nova-proxy",
  "X-Model-Version": "v2.1",
  "X-Request-Source": "api-gateway"
}
```

### Content & Accept Headers
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json",
  "Accept-Encoding": "gzip, deflate"
}
```

### Webhooks & Callbacks
```json
{
  "X-Webhook-URL": "https://your-domain.com/webhooks/partner",
  "X-Callback-URL": "https://your-domain.com/callbacks/results",
  "X-Notification-URL": "https://your-domain.com/notifications"
}
```

## ✅ Benefits

1. **🔐 Secure Authentication**: Centralized API key and token management
2. **🎯 Partner-Specific**: Different headers for different partners
3. **🔄 Automatic Injection**: No need to modify client requests
4. **📊 Debug Visibility**: Headers visible in debug responses
5. **⚖️ Load Balancing**: Different headers per upstream endpoint
6. **🛡️ Override Protection**: Hardcoded headers always take precedence

This feature provides enterprise-grade header management for complex partner integrations! 🚀
