version: '3.8'

services:
  # Nova Proxy Application
  nova-proxy:
    build:
      context: .
      dockerfile: Dockerfile.local
    ports:
      - "8080:8080"   # Main API
      - "10001:10001" # Metrics
      - "11001:11001" # Health checks
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs
    environment:
      - GIN_MODE=release
    networks:
      - monitoring
    restart: unless-stopped

  # VictoriaMetrics for metrics collection
  victoriametrics:
    image: victoriametrics/victoria-metrics:latest
    ports:
      - "8428:8428"
    volumes:
      - ./monitoring/victoriametrics.yml:/etc/victoriametrics/scrape.yml
      - victoriametrics_data:/victoria-metrics-data
    command:
      - '--storageDataPath=/victoria-metrics-data'
      - '--httpListenAddr=:8428'
      - '--retentionPeriod=12'
      - '--promscrape.config=/etc/victoriametrics/scrape.yml'
      - '--promscrape.configCheckInterval=10s'
      - '--search.maxQueryDuration=30s'
      - '--search.maxConcurrentRequests=16'
    networks:
      - monitoring
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning/datasources:/etc/grafana/provisioning/datasources
      - ./monitoring/grafana/provisioning/dashboards/dashboards.yml:/etc/grafana/provisioning/dashboards/dashboards.yml
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS=
      - GF_INSTALL_PLUGINS=
    networks:
      - monitoring
    restart: unless-stopped
    depends_on:
      - victoriametrics

networks:
  monitoring:
    driver: bridge

volumes:
  victoriametrics_data:
  grafana_data: